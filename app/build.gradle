plugins {
    id 'com.android.application'
    id 'com.didi.drouter'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    id 'com.google.gms.google-services'
}
apply plugin: 'com.mob.sdk'
boolean isDebug = gradle.startParameter.taskNames.any { it.toLowerCase().contains('debug') }
if (!isDebug) {
    apply plugin: 'com.google.firebase.crashlytics'
}
drouter {
    incremental = false
}

android {
    compileSdkVersion MyVersions.compileSdkVersion

    defaultConfig {
        applicationId 'com.merit.sport'
        minSdkVersion MyVersions.minSdkVersion
        targetSdkVersion MyVersions.targetSdkVersion
        versionCode MyVersions.versionCode
        versionName MyVersions.versionName
        flavorDimensions "Merach"
        manifestPlaceholders = [CHANNEL_NAME: "Merach"]
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
    }

    splits {
        abi {
            enable true
            reset()
            include 'armeabi-v7a', 'arm64-v8a'
            universalApk true
        }
    }

    signingConfigs {
        config {
            keyAlias 'merach'
            keyPassword 'merach'
            storeFile file('../yudong.keystore')
            storePassword 'merach'
        }
    }


    buildTypes {
        release {
            signingConfig signingConfigs.config
            minifyEnabled false
            shrinkResources false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.config
            debuggable true
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

    }


    buildFeatures {
        dataBinding = true
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
    packagingOptions {
        exclude '/lib/mips/*'
        exclude '/lib/mips64/*'
        exclude '/lib/x86/*'
        exclude '/lib/x86_64/*'
        exclude '/lib/armeabi/*'
    }
    productFlavors() {
        Google {}
        Amazon {}
        productFlavors.all
                {
                    flavor -> flavor.manifestPlaceholders = [CHANNEL_NAME: name]
                }
    }

}


dependencies {
    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
        resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
    }
    implementation project(':moduleCommon')
    implementation(project(":moduleCore:moduleCourse"))
    implementation(project(":moduleCore:moduleDevice"))
    implementation(project(":moduleCore:moduleLogin"))
    implementation(project(":moduleCore:moduleMe"))
    implementation(project(":moduleCore:modulePlayer"))
    implementation(project(":moduleCore:moduleWebview"))
    implementation(project(":moduleCore:moduleReport"))
    //SplashScreen向下兼容包
    implementation "androidx.core:core-splashscreen:1.0.0"
    testImplementation 'junit:junit:4.12'

}
MobSDK {
    appKey "3aa96a4a9db5a"
    appSecret "79dad3f8a225aa931765bc79176ad9c5"

    ShareSDK {

        devInfo {

            Facebook {
                id 8
                sortId 8
                appKey "1219934702428324"
                appSecret "0e150a34210c3dfd1fc602ea0c50797a"
                enable true
            }
            Instagram {
                id 28
                sortId 28
                appId "8873076616132663"
                appSecret "9375db2f0389a21e7714e0444cc58bab"
                enable true
            }
        }
    }
}
