<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".dialog.UpdateDialog">

    <data>

        <import type="com.merit.internation.R" />

        <variable
            name="v"
            type="com.merit.internation.dialog.UpdateDialog" />
        <variable
            name="key"
            type="com.merit.common.StringKeyConstant" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="55dp"
            app:bl_corners_radius="12dp"
            app:bl_solid_color="@color/white"
            app:layout_constraintBottom_toBottomOf="@+id/ivBottom"
            app:layout_constraintLeft_toLeftOf="@+id/tvContent"
            app:layout_constraintRight_toRightOf="@+id/tvContent"
            app:layout_constraintTop_toTopOf="@+id/ivIcon"
            tools:background="@color/white" />

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/icon_update"
            app:layout_constraintBottom_toTopOf="@+id/tvTitle"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            vb_text_str="@{key.pop_version_update_tx}"
            vb_text_id="@{@string/string_new_version}"
            android:textColor="#ff333333"
            android:textSize="18sp"
            app:layout_constraintBottom_toTopOf="@+id/tvContent"
            app:layout_constraintLeft_toLeftOf="@+id/tvContent"
            app:layout_constraintRight_toRightOf="@+id/tvContent" />

        <TextView
            android:id="@+id/tvContent"
            android:layout_width="0dp"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:layout_marginStart="40dp"
            android:layout_marginEnd="40dp"
            android:lineSpacingExtra="3dp"
            android:padding="17dp"
            vb_text_str="@{key.pop_version_update_msg}"
            vb_text_id="@{@string/string_new_version_content}"
            android:textColor="#ff1c1a1a"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            vb_click="@{v}"
            android:id="@+id/tvCancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:gravity="center"
            android:minHeight="44dp"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            vb_text_str="@{key.pop_version_update_cancel_bt}"
            vb_text_id="@{@string/string_update_later}"
            android:textColor="#ffff2451"
            android:textSize="18sp"
            android:visibility="gone"
            tools:visibility="visible"
            app:bl_corners_radius="22dp"
            app:bl_solid_color="@color/white"
            app:bl_stroke_color="#ffff2451"
            app:bl_stroke_width="1dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvConfirm"
            app:layout_constraintLeft_toLeftOf="@+id/tvContent"
            app:layout_constraintTop_toBottomOf="@+id/tvContent"
             />

        <TextView
            vb_click="@{v}"
            android:id="@+id/tvConfirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="15dp"
            android:gravity="center"
            android:minHeight="44dp"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            vb_text_str="@{key.pop_version_update_confirm_bt}"
            vb_text_id="@{@string/string_update_immediately}"
            android:textColor="#ffffffff"
            android:textSize="18sp"
            app:bl_corners_radius="22dp"
            app:bl_solid_color="#FF2451"
            app:layout_constraintLeft_toRightOf="@+id/tvCancel"
            app:layout_constraintRight_toRightOf="@+id/tvContent"
            app:layout_constraintTop_toBottomOf="@+id/tvContent"
            tools:background="#FF2451" />

        <ImageView
            android:id="@+id/ivBottom"
            app:layout_constraintTop_toBottomOf="@+id/tvConfirm"
            app:layout_constraintLeft_toRightOf="@+id/tvCancel"
            app:layout_constraintRight_toRightOf="@+id/tvContent"
            android:layout_width="match_parent"
            android:layout_height="20dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>