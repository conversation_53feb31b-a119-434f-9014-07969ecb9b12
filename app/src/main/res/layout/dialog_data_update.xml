<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".dialog.DataUpdateDialog">

    <data>

        <variable
            name="key"
            type="com.merit.common.StringKeyConstant" />

        <variable
            name="v"
            type="com.merit.internation.dialog.DataUpdateDialog" />

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
       >

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginTop="100dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            app:bl_corners_radius="4dp"
            app:bl_solid_color="#cc000000"
            >

            <TextView
                android:layout_width="240dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="12dp"
                android:layout_centerVertical="true"
                vb_text_str="@{key.pop_compensate_success_tx}"
                vb_text_id="@{@string/pop_compensate_success_tx}"
                tools:text="已成功修复异常运动数据，可在运动记录里查看"
                android:textColor="#ffF7F7FA"
                android:textSize="14sp"
                />

            <TextView
                android:id="@+id/tvLook"
                vb_click="@{v}"
                android:layout_alignParentEnd="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_centerVertical="true"
                android:textColor="#ffFFFFFF"
                android:textSize="14sp"
                vb_text_str="@{key.pop_compensate_success_check_bt}"
                vb_text_id="@{@string/pop_compensate_success_check_bt}"
                tools:text="立即查看"
                app:bl_corners_radius="4dp"
                android:padding="4dp"
                app:bl_solid_color="#FF2451"
                />


        </RelativeLayout>



    </RelativeLayout>

</layout>