<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">


    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:visibility="gone"
            android:id="@+id/mIv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/bg_login" />

        <FrameLayout
            android:id="@+id/sf"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">


                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/view_bg" />

                <ImageView
                    android:id="@+id/ivLogo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="32dp"
                    android:layout_marginBottom="312dp"
                    android:src="@mipmap/app_logo_splash" />

                <TextView
                    android:id="@+id/tv_splash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="32dp"
                    android:layout_marginBottom="283dp"
                    android:text="@string/welcome_tip1"
                    android:textColor="#ffffff"
                    android:textSize="24sp" />

                <TextView
                    android:id="@+id/btn_to_start"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="32dp"
                    android:layout_marginEnd="32dp"
                    android:layout_marginBottom="161dp"
                    android:background="@drawable/a_btn_red_bg"
                    android:gravity="center"
                    android:paddingTop="14dp"
                    android:paddingBottom="14dp"
                    android:text="@string/welcome_btn_tip1"
                    android:textColor="#ffffff"
                    android:textSize="14sp" />


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="120dp">


                    <TextView
                        android:id="@+id/show_tip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/welcome_tip2"
                        android:textColor="#E8E8E8"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/sing_in"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/welcome_tip3"
                        android:textColor="#FF2451"
                        android:textSize="14sp" />

                </LinearLayout>


            </RelativeLayout>


        </FrameLayout>


        <LinearLayout
            android:id="@+id/countDownLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_margin="40dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="8dp"
            android:paddingTop="4dp"
            android:paddingEnd="8dp"
            android:paddingBottom="4dp"
            app:bl_corners_radius="14dp"
            app:bl_solid_color="#661C1A1A">

            <TextView
                android:id="@+id/advertCountdownTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="15sp"
                tools:text="3s" />

            <ImageView
                android:id="@+id/advertCountdownIv"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginStart="2dp"
                android:src="@drawable/base_icon_close"
                android:visibility="gone" />
        </LinearLayout>

    </FrameLayout>


</layout>