<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.merit.me.R" />

        <variable
            name="v"
            type="com.merit.internation.dialog.DataCenterDialog" />

        <variable
            name="key"
            type="com.merit.common.StringKeyConstant" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:background="@color/black">

        <ImageView
            android:id="@+id/iv"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="154dp"
            android:src="@drawable/data_center_guide"
            app:layout_constraintDimensionRatio="343:218"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/descTv"
            vb_text_id="@{@string/page_data_updata_title}"
            vb_text_str="@{key.page_data_updata_title}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingLeft="32dp"
            android:paddingRight="32dp"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv"
            tools:text="运动数据每日都会更新哦,去锻炼产生数据吧～" />


        <TextView
            android:id="@+id/goTrainTv"
            vb_click="@{v}"
            vb_text_id="@{@string/page_data_updata_go_btn}"
            vb_text_str="@{key.page_data_updata_go_btn}"
            vb_type_face_num="@{3}"
            android:layout_width="130dp"
            android:layout_height="48dp"
            android:layout_marginTop="32dp"
            android:gravity="center"
            android:padding="10dp"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            app:bl_corners_radius="12dp"
            app:bl_solid_color="#FF2451"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/descTv"
            app:vb_click="@{v}"
            tools:background="#FF2451"
            tools:text="@string/page_data_updata_go_btn" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>