<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".dialog.CacheManagementDialog">

    <data>

        <import type="com.merit.me.R" />

        <variable
            name="v"
            type="com.merit.internation.dialog.SportGuideDialog" />
        <variable
            name="key"
            type="com.merit.common.StringKeyConstant" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:layout_centerInParent="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="40dp"
            android:layout_marginEnd="40dp"
            app:cardBackgroundColor="@android:color/white"
            app:cardCornerRadius="12dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tvTitle"
                    vb_text_bold="@{true}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="9dp"
                    android:gravity="center"
                    android:paddingTop="21dp"
                    android:singleLine="true"
                    vb_text_str="@{key.btn_data_guide_into_trainmode_popup_title}"
                    vb_text_id="@{@string/btn_data_guide_into_trainmode_popup_title}"
                    android:textColor="#1C1A1A"
                    android:textSize="14.5sp" />

                <TextView
                    android:id="@+id/tvContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tvTitle"
                    android:layout_marginBottom="30dp"
                    android:gravity="center"
                    android:lineSpacingExtra="3dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:scrollbars="vertical"
                    vb_text_str="@{key.btn_data_guide_into_trainmode_popup_illustrate}"
                    vb_text_id="@{@string/btn_data_guide_into_trainmode_popup_illustrate}"
                    android:textColor="#1C1A1A"
                    android:textSize="14.5sp" />

                <View
                    android:id="@+id/tvLine"
                    style="@style/vb_line_horizontal"
                    android:layout_below="@+id/tvContent" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tvLine"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvLeft"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:padding="15dp"
                        vb_text_str="@{key.btn_data_guide_into_trainmode_popup_cancel}"
                        vb_text_id="@{@string/btn_data_guide_into_trainmode_popup_cancel}"
                        android:textColor="#1C1A1A"
                        android:textSize="14sp"
                        app:vb_click="@{v}" />

                    <View style="@style/vb_line_vertical" />

                    <TextView
                        android:id="@+id/tvRight"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:padding="15dp"
                        vb_text_str="@{key.btn_data_guide_into_trainmode_popup_enter}"
                        vb_text_id="@{@string/btn_data_guide_into_trainmode_popup_enter}"
                        android:textColor="#FF2451"
                        android:textSize="14sp"
                        app:vb_click="@{v}" />
                </LinearLayout>
            </RelativeLayout>
        </androidx.cardview.widget.CardView>

    </RelativeLayout>

</layout>