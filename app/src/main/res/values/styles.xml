<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="SplashScreenTheme" parent="SplashScreenTheme.Base">
        <item name="windowSplashScreenAnimatedIcon">@drawable/icon_launcher_sp_new</item>
    </style>

    <style name="SplashScreenTheme.Base" parent="Theme.SplashScreen">
        <item name="colorPrimary">@android:color/black</item>
        <item name="colorPrimaryDark">@android:color/black</item>
        <item name="colorAccent">@android:color/black</item>

        <item name="android:statusBarColor">@android:color/black</item>
        <item name="android:navigationBarColor">@android:color/black</item>
        <item name="android:windowLightNavigationBar">false</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- screen background color -->
        <item name="windowSplashScreenBackground">@android:color/black</item>

        <!-- target activity theme-->
        <item name="postSplashScreenTheme">@style/SplashThemeActivity</item>
    </style>

    <style name="SplashThemeActivity" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">#0D020F</item>
        <item name="android:windowFullscreen">true</item>
    </style>


    <style name="my_app_theme" parent="Theme.AppCompat.NoActionBar">

        <item name="colorPrimary">@android:color/transparent</item>

        <item name="colorAccent">@android:color/black</item>

        <item name="android:windowBackground">#F7F7FA</item>

        <item name="android:windowTranslucentStatus">true</item>

        <item name="android:statusBarColor">@android:color/transparent</item>

        <item name="colorPrimaryDark">@android:color/transparent</item>

    </style>


    <style name="SplashScreen" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/splash_bg</item>
<!--        <item name="android:windowBackground">@android:color/transparent</item>-->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:screenOrientation">portrait</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

</resources>