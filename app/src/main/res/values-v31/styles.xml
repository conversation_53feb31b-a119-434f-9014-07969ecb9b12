<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="SplashScreenTheme" parent="SplashScreenTheme.Base">
        <item name="android:windowSplashScreenBackground">#000000</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/icon_launcher_sp_new</item>
        <item name="android:windowSplashScreenAnimationDuration">500</item>
        <item name="android:windowSplashScreenBrandingImage">@drawable/icon_title_sp_new</item>
    </style>

    <style name="SplashScreen" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/splash_bg</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:screenOrientation">portrait</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSplashScreenBackground">#000000</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/icon_launcher_sp_new</item>
        <item name="android:windowSplashScreenAnimationDuration">500</item>
        <item name="android:windowSplashScreenBrandingImage">@drawable/icon_title_sp_new</item>
    </style>
</resources>