package com.merit.internation

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.merit.common.AppConstant
import com.merit.common.RouterConstant
import com.merit.common.bean.WebBean

/**
 * <AUTHOR>
 * desc:
 * @time   2024/8/6
 */
class PermissionUsageRationaleActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val bundle = Bundle()
        bundle.putParcelable(
            RouterConstant.RouterWebView.BEAN,
            WebBean(url = AppConstant.URL_AGREEMENT, isShowTitle = true)
        )
        RouterConstant.RouterWebView().go(this, bundle)
        finish()
    }
}