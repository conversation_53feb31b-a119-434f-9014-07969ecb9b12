package com.merit.internation.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.merit.common.AppConstant
import com.merit.common.bean.SettlementSuccessBean
import com.merit.common.viewmodel.BaseViewModel
import com.merit.internation.bean.AdvertBean
import com.merit.internation.bean.LocalAdvertBean
import com.merit.modulereport.database.TrainDataWrapper
import com.mrk.common.CommonApplication
import com.mrk.network.net.MrkNetwork
import com.mrk.network.net.request
import com.tencent.mmkv.MMKV
import com.v.log.util.log
import kotlinx.coroutines.launch

/**
 * author  : ww
 * desc    : Main
 * time    : 2021-12-26 16:30:01
 */
class MainViewModel : BaseViewModel() {
    var advertLiveData = MutableLiveData<AdvertBean>()
    fun pushConfig(params: Map<String, Any>) {
        request(
            {
                MrkNetwork.instance.post("/user/login/log", params)
            }, MutableLiveData<String>()
        )
    }


    //数据上报异常结算上报
    /*
    * items	object []	sportReportId	integer
    * */
    fun toReportData(dataList: List<TrainDataWrapper>, success: ((List<SettlementSuccessBean>) -> Unit), error: (() -> Unit)) {
        val params = mapOf(
            "items" to dataList
        )
        request({
            MrkNetwork.instance.post("/user/sport-result/compensate-settlement", map = params)
        }, MutableLiveData<List<SettlementSuccessBean>>(), success = {
            success.invoke(it)
        }, error = {
            error.invoke()
        })
    }

    fun getSportCount(success: ((Int) -> Unit)) {
        request({
            MrkNetwork.instance.get("/user/sport-result/count")
        }, MutableLiveData<String>(), success = {
            success.invoke(it.toInt())
        })
    }


    fun getAdvert() {
        request(
            {
                MrkNetwork.instance.get(
                    "/app/advert/getAdvert", mapOf(
                        "positionCode" to "init_page_click", "channel" to "merach_app_external"
                    )
                )
            }, advertLiveData
        )
    }

    fun handleAdvert(advertBean: AdvertBean) {
        viewModelScope.launch {
            if (advertBean.adverts.isEmpty()) {
                MMKV.defaultMMKV().putString(AppConstant.MMKV_CURRENT_ADVERT, "")
                return@launch
            }

            val jsonString = MMKV.defaultMMKV().decodeString(AppConstant.MMKV_LOCAL_ADVERT_LIST, "")
            val advertLocalBeanList = if (jsonString.isNullOrEmpty()) {
                emptyList<LocalAdvertBean>()
            } else {
                val type = object : TypeToken<List<LocalAdvertBean>>() {}.type
                Gson().fromJson(jsonString, type)
            }
            "handleAdvert advertLocalBeanList = $advertLocalBeanList".log()
            if (advertLocalBeanList.isEmpty()) {
                saveNewAdvert(advertLocalBeanList, advertBean, 0)
            } else {
                advertBean.adverts.forEachIndexed { index, advert ->
                    val containsIndex = advertLocalBeanList.indexOfFirst { it.id == advert.id }
                    "handleAdvert find id = ${advert.id} ".log()
                    if (containsIndex == -1) {
                        //说明本地没有存储过这个广告 直接存储 (已经按照后端给的顺序排序)
                        saveNewAdvert(advertLocalBeanList, advertBean, index)
                        return@launch
                    } else {
                        "handleAdvert displayedCount = ${advertLocalBeanList[containsIndex].displayedCount} advert.displayCount=${advert.displayCount}".log()
                        if (advertLocalBeanList[containsIndex].displayedCount >= advert.displayCount) {
                            saveNewAdvert(advertLocalBeanList, advertBean, index)
                            //说明次数超过了 继续取列表的下一个
                            return@forEachIndexed
                        }
                        //次数没超过 保存下当前的广告和预加载图片即可
                        MMKV.defaultMMKV().putString(AppConstant.MMKV_CURRENT_ADVERT, Gson().toJson(advert))
                        Glide.with(CommonApplication.instance).load(advert.image).preload()
                        return@launch
                    }
                }
            }
        }
    }

    private fun saveNewAdvert(advertLocalBeanList: List<LocalAdvertBean>, advertBean: AdvertBean, index: Int) {
        val tempAdvertLocalBeanList = mutableListOf<LocalAdvertBean>()
        tempAdvertLocalBeanList.addAll(advertLocalBeanList)
        tempAdvertLocalBeanList.add(LocalAdvertBean(advertBean.adverts[index].id, 0))
        MMKV.defaultMMKV().putString(AppConstant.MMKV_LOCAL_ADVERT_LIST, Gson().toJson(tempAdvertLocalBeanList))
        MMKV.defaultMMKV().putString(AppConstant.MMKV_CURRENT_ADVERT, Gson().toJson(advertBean.adverts[index]))
        Glide.with(CommonApplication.instance).load(advertBean.adverts[index].image).preload()
    }

}