package com.merit.internation.viewmodel

import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.merit.common.AppConstant
import com.merit.common.MyApplication
import com.merit.common.bean.LanguageBean
import com.merit.common.utils.getNetLanguage
import com.merit.common.viewmodel.BaseViewModel
import com.merit.internation.bean.AdvertBean
import com.merit.internation.bean.LocalAdvertBean
import com.merit.internation.bean.SplashBean
import com.mrk.network.net.MrkNetwork
import com.mrk.network.net.request
import com.tencent.mmkv.MMKV
import com.v.base.utils.vbGetAppVersionName

/**
 * author  : ww
 * desc    : 开屏
 * time    : 2021-12-26 16:30:01
 */
class SplashViewModel : BaseViewModel() {

    var splashBean = MutableLiveData<SplashBean>()

    var languageInfo = MutableLiveData<LanguageBean>()

    var advertLiveBean = MutableLiveData<AdvertBean.Advert?>()

    fun getData(versionName: String) {
        getVersion(versionName)
    }


    fun getVersion(versionName: String) {
        val params = mapOf(
            "terminal" to "1", "name" to versionName
        )
        request(
            {
                MrkNetwork.instance.get("/user/feedback/version", params)
            }, splashBean
        )
    }


    fun getLanguage(success: (() -> Unit)? = null, error: (() -> Unit)? = null) {
        val languageCurrent = getNetLanguage()
        val appVersion = MyApplication.instance.vbGetAppVersionName()
        val params = mapOf(
            "languageTag" to languageCurrent, "languageVersion" to appVersion, "updateTime" to "", "terminal" to 2
        )
        request({
            MrkNetwork.instance.post("/app/i18n-data/list", params)
        }, languageInfo, success = {
            success?.invoke()
        }, error = {
            error?.invoke()
        })
    }


    fun handleAdvert() {
        val currentAdvertJson = MMKV.defaultMMKV().getString(AppConstant.MMKV_CURRENT_ADVERT, "")
        if (currentAdvertJson.isNullOrEmpty()) {
            advertLiveBean.postValue(null)
            return
        }
        val advertSubBean = Gson().fromJson(currentAdvertJson, AdvertBean.Advert::class.java)
        val jsonString = MMKV.defaultMMKV().decodeString(AppConstant.MMKV_LOCAL_ADVERT_LIST, "")
        val advertLocalBeanList = if (jsonString.isNullOrEmpty()) {
            emptyList<LocalAdvertBean>()
        } else {
            val type = object : TypeToken<List<LocalAdvertBean>>() {}.type
            Gson().fromJson(jsonString, type)
        }
        val localAdvert = advertLocalBeanList.find { it.id == advertSubBean.id }
        if (localAdvert == null) {
            advertLiveBean.postValue(null)
            return
        }
        if (localAdvert.displayedCount >= advertSubBean.displayCount) {
            advertLiveBean.postValue(null)
            return
        }
        val tempAdvertLocalBeanList = mutableListOf<LocalAdvertBean>()
        tempAdvertLocalBeanList.addAll(advertLocalBeanList)
        val findLocalBean = tempAdvertLocalBeanList.firstOrNull { it.id == advertSubBean.id }
        findLocalBean?.apply {
            displayedCount += 1
        }
        MMKV.defaultMMKV().putString(AppConstant.MMKV_LOCAL_ADVERT_LIST, Gson().toJson(tempAdvertLocalBeanList))
        advertLiveBean.postValue(advertSubBean)
    }

}