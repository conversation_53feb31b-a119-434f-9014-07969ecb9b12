package com.merit.internation

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.core.os.bundleOf
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.logEvent
import com.merit.common.AppConstant
import com.merit.common.BuildConfig
import com.merit.common.MyApplication.Companion.firebaseAnalytics
import com.merit.common.RouterConstant
import com.merit.common.commonViewModel
import com.merit.common.utils.FlutterUtils
import com.merit.common.utils.IntentApp
import com.merit.common.utils.StringManagerUtils
import com.merit.common.utils.getChannel
import com.merit.internation.bean.SplashBean
import com.merit.internation.databinding.ActivitySplashBinding
import com.merit.internation.dialog.UpdateDialog
import com.merit.internation.bean.AdvertBean
import com.merit.internation.viewmodel.SplashViewModel
import com.tencent.mmkv.MMKV
import com.v.base.VBActivity
import com.v.base.utils.*
import com.v.log.util.log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


class SplashActivity : VBActivity<ActivitySplashBinding, SplashViewModel>() {

    var what: String = ""

    var splashBean: SplashBean? = null
    var advertLiveBeanId = ""
    private var handler = Handler(Looper.getMainLooper())
    private var countdownJob: Job? = null
    var firstStarted: Boolean = true
    override fun useTranslucent(): Boolean {
        return true
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        firstStarted = MMKV.defaultMMKV().getBoolean(AppConstant.MMKV_SPASH, true)
        if (firstStarted) {
            mDataBinding.mIv.vbVisible()
            mDataBinding.sf.vbVisible()
        } else {
            mDataBinding.sf.vbGone()
        }
        startBgAnim()
        mDataBinding.countDownLayout.setOnClickListener {
            firebaseAnalytics.logEvent(
                "page_openscreen_close", bundleOf(
                    "openscreen_id" to advertLiveBeanId
                )
            )
            if (mDataBinding.advertCountdownIv.visibility == View.VISIBLE) {
                advertCountDownEnd()
            }

        }
    }

    override fun initData() {
        mViewModel.getLanguage()
        mViewModel.getData(<EMAIL>())
    }

    @SuppressLint("SetTextI18n")
    override fun createObserver() {
        mViewModel.advertLiveBean.observe(this) { advertLiveBean ->
            "receive advertLiveBean".log()
            if (advertLiveBean != null) {
                advertLiveBeanId = advertLiveBean.id
                firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
                    param(FirebaseAnalytics.Param.SCREEN_NAME, "page_openscreen_exposure")

                }
                handler.postDelayed({
                    mDataBinding.mIv.vbVisible()
                    mDataBinding.mIv.vbLoad(advertLiveBean.image)
                    mDataBinding.advertCountdownTv.text = "${advertLiveBean.displayDuration} s"

                    val seconds = advertLiveBean.displayDuration
                    countdownJob = CoroutineScope(Dispatchers.Main).launch {
                        for (i in seconds downTo 1) {
                            mDataBinding.advertCountdownTv.text = " $i s "
                            delay(1000)
                        }
                        mDataBinding.countDownLayout.vbGone()
                        advertCountDownEnd()
                    }

                    handler.postDelayed({
                        mDataBinding.advertCountdownIv.vbVisible()
                    }, advertLiveBean.closeSecond.toLong() * 1000)
                    mDataBinding.mIv.setOnClickListener {
                        handleAdvertAction(advertLiveBean)
                    }
                }, 300)
            } else {
                handleSplashBean(splashBean)
            }
        }
        mDataBinding.singIn.setOnClickListener {
            MMKV.defaultMMKV().putBoolean(AppConstant.MMKV_SPASH, false)
            what = AppConstant.MMKV_SIGIN
            handleSplashBean(splashBean)
        }

        mDataBinding.btnToStart.setOnClickListener {
            MMKV.defaultMMKV().putBoolean(AppConstant.MMKV_SPASH, false)
            what = AppConstant.MMKV_REGSTER
            handleSplashBean(splashBean)
        }

        mViewModel.languageInfo.observe(this) {
            it.run {
                StringManagerUtils.setStrData(it.data)
                FlutterUtils.setLanguage(StringManagerUtils.hashMap)
            }
        }

        mViewModel.splashBean.observe(this) {
            FlutterUtils.setHeader()
            splashBean = it
            if (!firstStarted) {
                mViewModel.handleAdvert()
            }
        }
    }

    private fun handleAdvertAction(advertLiveBean: AdvertBean.Advert) {
        firebaseAnalytics.logEvent("advert_click_${advertLiveBean.id}", bundleOf("openscreen_id" to advertLiveBean.id))
        firebaseAnalytics.logEvent("page_openscreen_click", bundleOf("openscreen_id" to advertLiveBean.id))
        advertCountDownEnd(advertLiveBean)
    }

    private fun advertCountDownEnd(advertLiveBean: AdvertBean.Advert? = null) {
        countdownJob?.cancel()
        "advertCountDownEnd splashBean=$splashBean advertLiveBean=$advertLiveBean".log()
        handleSplashBean(splashBean, advertLiveBean)
    }

    private fun handleSplashBean(splashBean: SplashBean?, advertLiveBean: AdvertBean.Advert? = null) {
        if (splashBean == null) {
            dispose(advertLiveBean)
        } else {
            //测试环境 不显示更新弹窗
            if (BuildConfig.IS_DEBUG) {
                dispose(advertLiveBean)
            } else {
                if (splashBean.version > <EMAIL>()) {
                    UpdateDialog(mContext, splashBean).setClickListener { dialog, position ->
                        if (position == 1) {
                            updateChannel()
                        } else {
                            dispose(advertLiveBean)
                        }
                        dialog.dismiss()
                    }.show()
                } else {
                    dispose(advertLiveBean)
                }
            }
        }
    }

    private fun dispose(advertLiveBean: AdvertBean.Advert? = null) {
        "SplashActivity dispose advertLiveBean=$advertLiveBean".log()
        if (commonViewModel.loginBean.value == null) {
            RouterConstant.RouterLogin().go(mContext, what)
            overridePendingTransition(R.anim.fadein_in, R.anim.fadein_out)
            finish()
        } else {
            <EMAIL>(MainActivity::class.java)
            if (advertLiveBean != null) {
                IntentApp.intent(
                    this, contentType = advertLiveBean.contentType, content = advertLiveBean.content
                )
            }
            finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countdownJob?.cancel()
        handler.removeCallbacksAndMessages(null)
    }

    //渠道更新处理
    private fun updateChannel() {
        if (getChannel() == "Amazon") {
            if (<EMAIL>("com.amazon.venezia")) {
                startAmazon()
            } else {
                startAmazonByBrowser()
            }
        } else {
            if (<EMAIL>("com.android.vending")) {
                startGooglePlay()
            } else {
                startGooglePlayByMarketUrl()
            }
        }
    }


    //跳转google play APP
    private fun startGooglePlay() {
        val intent = Intent("android.intent.action.VIEW")
        intent.setPackage("com.android.vending")
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        intent.data = Uri.parse("market://details?id=com.merit.sport")
        startActivity(intent)
    }


    //跳转google play网页
    private fun startGooglePlayByMarketUrl() {
        val intent = Intent("android.intent.action.VIEW")
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        intent.data = Uri.parse("https://play.google.com/store/apps/details?id=com.merit.sport")
        startActivity(intent)
    }


    //调起amazon
    private fun startAmazon() {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse("amzn://apps/android?p=com.merit.sport"))
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        startActivity(intent)
    }

    //浏览器调起amazon
    private fun startAmazonByBrowser() {
        val intent = Intent(
            Intent.ACTION_VIEW, Uri.parse("http://www.amazon.com/gp/mas/dl/android?p=com.merit.sport")
        )
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        startActivity(intent)
    }

    private fun startBgAnim() {
        if (!firstStarted) {
            return
        }
        val imageView = mDataBinding.mIv
        val scaleXUp = ObjectAnimator.ofFloat(imageView, "scaleX", 1f, 1.5f)
        val scaleYUp = ObjectAnimator.ofFloat(imageView, "scaleY", 1f, 1.5f)

        val scaleXDown = ObjectAnimator.ofFloat(imageView, "scaleX", 1.5f, 1f)
        val scaleYDown = ObjectAnimator.ofFloat(imageView, "scaleY", 1.5f, 1f)

        scaleXUp.duration = 1000 * 20
        scaleYUp.duration = 1000 * 20

        scaleXDown.duration = 1000 * 20
        scaleYDown.duration = 1000 * 20

        val animatorSet = AnimatorSet()
        animatorSet.play(scaleXUp).with(scaleYUp)
        animatorSet.play(scaleXDown).with(scaleYDown).after(scaleXUp)

        animatorSet.addListener(object : Animator.AnimatorListener {
            override fun onAnimationRepeat(animation: Animator) {}
            override fun onAnimationEnd(animation: Animator) {
                animatorSet.start()
            }

            override fun onAnimationCancel(animation: Animator) {}
            override fun onAnimationStart(animation: Animator) {}
        })

        animatorSet.start()
    }
}