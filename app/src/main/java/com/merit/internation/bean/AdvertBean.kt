package com.merit.internation.bean

data class AdvertBean(
    val adverts: List<Advert> = listOf(), val channel: String = "", // merach_app
    val createId: String = "", // 1
    val createName: String = "", val createTime: String = "", // 2023-08-21 16:27:15
    val diagramUrl: String = "", // https://static.merach.com/other/20230821/05d11076d78a4d51886bdcc692359bc8_1242x1786.jpg
    val extParams: String = "", val id: String = "", // 1000000007
    val isDelete: Int = 0, // 0
    val layout: String = "", // multiple
    val positionCode: String = "", // init_page_click
    val positionName: String = "", // 开屏页
) {
    data class Advert(
        val coachAvatar: String = "",
        val code: String = "",
        val content: String = "", // https://testconsole.merach.com/webview/many-page/#/custom-pay

        val contentType: String = "", // app_h5
        val id: String = "", // 2000000017
        val image: String = "", // https://static.merach.com/other/20230821/ca4ec83f9a534cf2be09b2ea2c045dc2_1242x1786.jpg
        val positionCode: String = "", // init_page_click
        val positionInfoId: String = "", // 1000000007
        val displayCount: Int = 0,
        val displayDuration: Int = 0,
        val closeSecond: Int = 0, // 2
    )
}