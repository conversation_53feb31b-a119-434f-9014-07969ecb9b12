package com.merit.internation

import androidx.core.os.bundleOf
import androidx.fragment.app.FragmentPagerAdapter
import androidx.lifecycle.lifecycleScope
import androidx.viewpager.widget.ViewPager.OnPageChangeListener
import com.cc.control.protocol.DeviceConstants
import com.didi.drouter.annotation.Router
import com.didi.drouter.api.DRouter
import com.didi.drouter.page.IPageBean
import com.didi.drouter.page.IPageRouter
import com.didi.drouter.page.RouterPageViewPager
import com.didi.drouter.store.ServiceKey
import com.jeremyliao.liveeventbus.LiveEventBus
import com.merit.common.AppConstant
import com.merit.common.GAConstant
import com.merit.common.MyApplication
import com.merit.common.RouterConstant
import com.merit.common.StringKeyConstant
import com.merit.common.bean.LocationBean
import com.merit.common.commonViewModel
import com.merit.common.utils.FlutterUtils
import com.merit.common.utils.HealthConnectUtil
import com.merit.common.utils.StringManagerUtils
import com.merit.common.views.IndicatorZoom
import com.merit.common.views.OnItemClickListener
import com.merit.course.views.NoDeviceInteractionDialog
import com.merit.device.DeviceRouter
import com.merit.device.dialog.MyDeviceDialog
import com.merit.internation.databinding.ActivityMainBinding
import com.merit.internation.dialog.DataCenterDialog
import com.merit.internation.dialog.DataUpdateDialog
import com.merit.internation.dialog.SportGuideDialog
import com.merit.internation.util.SystemUtil
import com.merit.internation.viewmodel.MainViewModel
import com.merit.modulereport.database.DatabaseUtil
import com.mob.MobSDK
import com.mrk.device.MrkDeviceManger
import com.mrk.device.bean.DeviceConnectEnum
import com.mrk.device.bean.DeviceMangerBean
import com.mrk.device.bean.DeviceMyBindBean
import com.mrk.device.device.DeviceListener
import com.sea.flutter_middleware.FlutterMiddleware
import com.tencent.mmkv.MMKV
import com.v.base.VBActivity
import com.v.base.utils.vbGetAppVersionName
import com.v.base.utils.vbGetDeviceId
import com.v.base.utils.vbToBean
import com.v.base.utils.vbToast
import com.v.log.util.log
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale


/**
 * author  : ww
 * desc    :
 * time    : 2021/12/18
 */
@Router(path = RouterConstant.RouterMainActivity.PATH)
class MainActivity : VBActivity<ActivityMainBinding, MainViewModel>() {


    override fun useTranslucent(): Boolean {
        return true
    }

    private val aliasRouter = "main_activity_router_page_viewpager"

    private val pageRouter by lazy {
        RouterPageViewPager(
            supportFragmentManager, mDataBinding.viewPager, FragmentPagerAdapter.BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
        ).apply {
            update(
                IPageBean.DefPageBean(RouterConstant.ROUTER_FRAGMENT_COURSE),
                IPageBean.DefPageBean(RouterConstant.ROUTER_FRAGMENT_WORKOUTS),
                IPageBean.DefPageBean(RouterConstant.ROUTER_FRAGMENT_ME)
            )
        }
    }

    private val commonNavigator by lazy {
        val titles = arrayOf(
            StringManagerUtils.getString(StringKeyConstant.btn_train, R.string.btn_train),
            StringManagerUtils.getString(StringKeyConstant.btn_data, R.string.btn_data),
            StringManagerUtils.getString(StringKeyConstant.btn_profile, R.string.btn_profile),
        )

        //  val titles = resources.getStringArray(R.array.dm_tab)

        val iconOffs = arrayOf(
            R.mipmap.icon_tab_train_off, R.mipmap.icon_tab_workouts_off, R.mipmap.icon_tab_me_off
        )
        val iconOns = arrayOf(
            R.mipmap.icon_tab_train_on, R.mipmap.icon_tab_workouts_on, R.mipmap.icon_tab_me_on
        )

        IndicatorZoom(this,
            mDataBinding.viewPager,
            titles,
            iconOffs,
            iconOns,
            onItemClickListener = object : OnItemClickListener {
                override fun onItemClick(position: Int) {
                    LiveEventBus.get<Int>(AppConstant.MAIN_TAB_CLICK).post(position)
                }

            })

    }

    override fun initData() {
        MobSDK.submitPolicyGrantResult(true)
        FlutterUtils.setHeader()
        FlutterUtils.setLanguage(StringManagerUtils.getValueData())
        DRouter.register(
            ServiceKey.build(IPageRouter::class.java).setAlias(aliasRouter).setLifecycle(this.lifecycle), pageRouter
        )
        commonNavigator.create(mDataBinding.magicIndicator)
        initConfig()
        initDevice()

        mDataBinding.viewPager.addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(p0: Int, p1: Float, p2: Int) {

            }

            override fun onPageSelected(p0: Int) {
                when (p0) {
                    0 -> {
                        MyApplication.firebaseAnalytics.logEvent(
                            "btn_data", bundleOf()
                        )
                        needShowSportGuide()
                    }

                    1 -> {
                        needShowNoDeviceDialog()
                        MyApplication.firebaseAnalytics.logEvent(
                            "btn_train", bundleOf()
                        )
                    }

                    2 -> {
                        MyApplication.firebaseAnalytics.logEvent(
                            "btn_me", bundleOf()
                        )
                    }
                }
            }

            override fun onPageScrollStateChanged(p0: Int) {
            }
        })

        mDataBinding.viewPager.currentItem = 0
        mViewModel.getAdvert()
        HealthConnectUtil.readUserDataFromHealth()
        // showGuide360Version()
        intent.extras?.run {
            val isToSearchEquipment = getBoolean(RouterConstant.RouterMainActivity.IS_TO_SEARCH_EQUIPMENT, false)
            val productId = getString(RouterConstant.RouterMainActivity.PRODUCT_ID, "")
            if (isToSearchEquipment) {
                RouterConstant.RouterDeviceSearchActivity().go(mContext, productId)
            }
        }
    }

    fun needShowSportGuide() {
        mViewModel.getSportCount {
            if (it == 0) {
                showSportGuide()
            }
        }
    }

    private fun showSportGuide() {
        if (isConnectDevice()) {
            SportGuideDialog(this).setClickListener { dialog, position ->
                if (position == 1) {
                    MyApplication.firebaseAnalytics.logEvent("btn_data_guidetotrain_popups_go", bundleOf())
                    MrkDeviceManger.getDeviceMyBindList { list ->
                        if (list.isNotEmpty()) {
                            if (list.size == 1) {
                                goTrain(list[0])
                            } else {
                                MyApplication.firebaseAnalytics.logEvent(
                                    GAConstant.HOME.MULTI_DEVICE_ALERT, bundleOf()
                                )
                                MyDeviceDialog(mContext, list).setClickListener { dialog, bean ->
                                    goTrain(bean)
                                    dialog.dismiss()
                                }.show()
                            }
                        } else {
                            RouterConstant.RouterDeviceSearchActivity().go(mContext, "")
                        }
                    }
                } else {
                    MyApplication.firebaseAnalytics.logEvent("btn_data_guidetotrain_popups_close", bundleOf())
                }
            }.show()
        }
    }

    private fun needShowNoDeviceDialog() {
        MrkDeviceManger.getDeviceMyBindList { list ->
            if (list.isEmpty()) {
                val kv = MMKV.defaultMMKV()
                val lastLaunchDate = kv.getString("lastLaunchDate", null)
                val calendar = Calendar.getInstance()
                val currentDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(calendar.time)
                val isFirstLaunchOfDay = lastLaunchDate == null || lastLaunchDate != currentDate
                if (isFirstLaunchOfDay) {
                    kv.putString("lastLaunchDate", currentDate)
                    NoDeviceInteractionDialog(this).show()
                }

            }
        }
    }

    private fun isConnectDevice(): Boolean {
        MrkDeviceManger.getDeviceMap().forEach {
            if (it.value.connectEnum == DeviceConnectEnum.ON) {
                return true
            }
        }
        return false
    }

    //去自由训练
    private fun goTrain(recordBean: DeviceMyBindBean.Record) {
        if (MrkDeviceManger.isConnect(recordBean.mac)) {
            val bean = MrkDeviceManger.getDeviceMangerBean(recordBean.mac)?.deviceDetails
            if (bean != null) {
                val bundle = bundleOf(
                    DeviceRouter.DeviceFreeActivityRouter.DEVICE_CODE to bean.mac,
                    DeviceRouter.DeviceFreeActivityRouter.DEVICE_ONE_TYPE_NAME to bean.bluetoothName,
                    DeviceRouter.DeviceFreeActivityRouter.DEVICE_TYPE to bean.productId,
                    DeviceRouter.DeviceFreeActivityRouter.DEVICE_NAME to bean.modelId,
                    DeviceRouter.DeviceFreeActivityRouter.TARGET_TYPE to DeviceConstants.D_TRAIN_FREE,
                )
                DeviceRouter.DeviceFreeActivityRouter().go(mContext, bundle)
            }

        } else {
            RouterConstant.RouterDeviceInfoActivity()
                .go(mContext, MrkDeviceManger.formatDeviceGoConnectBean(recordBean), true)
        }
    }

    override fun createObserver() {
        LiveEventBus.get<Int>(AppConstant.EVENT_MAIN_TAB).observe(this) {
            mDataBinding.viewPager.currentItem = it
        }

        LiveEventBus.get<Int>(AppConstant.MMKV_UNIT).observe(this) {
            FlutterMiddleware.setUnit(it)
        }
        mViewModel.advertLiveData.observe(this) {
            mViewModel.handleAdvert(it)
        }
    }

    override fun onResume() {
        super.onResume()
        commonViewModel.getUserInfo()
        //数据上报成功，清空数据库
        "onResume -------------- onResume ".log("DatabaseUtil")
        lifecycleScope.launch {
            val daoList = DatabaseUtil.getAllTrainDataWrappers(mContext)
            "获取数据库信息 大小为${daoList.size} ".log("DatabaseUtil")
            daoList.forEach {
                "获取数据库信息子项目信息 sportResultId=${it.sportResultId} 大小为${it.compensateDatas.size} ".log("DatabaseUtil")
                it.compensateDatas.forEach { beans ->
                    "获取数据库信息 训练数据大小为${beans.trainDatas.size} ".log("DatabaseUtil")
                }
            }
            if (daoList.isNotEmpty()) {

                mViewModel.toReportData(daoList, success = { settlementSuccessBeans ->
                    "上报成功，开始清除数据库 上报数据${daoList}".log("DatabaseUtil")
                    settlementSuccessBeans.forEach { settlementSuccessBean ->
                        HealthConnectUtil.insertHealthData(settlementSuccessBean)
                    }

                    lifecycleScope.launch {
                        DatabaseUtil.deleteAll(mContext)
                        "上报成功，清除数据库 end".log("DatabaseUtil")
                    }
                    DataUpdateDialog(mContext).setClickListener {
                        RouterConstant.RouterExerciseDataActivity().go(mContext)
                    }.show()
                }, error = {
                    lifecycleScope.launch {
                        "上报失败，清除数据库 end".log("DatabaseUtil")
                        DatabaseUtil.deleteAll(mContext)
                    }
                })
            }
        }
        if (mDataBinding.viewPager.currentItem == 0) {
            needShowSportGuide()
        }
    }


    private fun initConfig() {

        try {
            val map = HashMap<String, String>()
            map["province"] = ""//省份
            map["city"] = ""//城市
            map["region"] = ""//区县
            map["address"] = ""//地址
            map["longitude"] = ""//经度
            map["latitude"] = ""//纬度

            val mmkvStr = MMKV.defaultMMKV().getString(AppConstant.MMKV_LOCATION, "")
            if (!mmkvStr.isNullOrEmpty()) {
                val bean = mmkvStr.vbToBean(LocationBean::class.java)
                map["province"] = bean.province//省份
                map["city"] = bean.city//城市
                map["address"] = bean.address//地址
                map["longitude"] = bean.longitude//经度
                map["latitude"] = bean.latitude//纬度
            }

            map["terminal"] = "1"//终端：1-Android，2-IOS
            map["deviceName"] = SystemUtil.getDeviceBrand() + "_" + SystemUtil.getSystemModel()//设备型号名称
            map["deviceNo"] = vbGetDeviceId().let {
                var s = vbGetDeviceId().replace("F", "")
                if (s.length > 64) {
                    s = s.substring(0, 64)
                } else {
                    s
                }
                s
            }
            map["deviceOs"] = SystemUtil.getSystemVersion()//设备系统
            map["appVersion"] = vbGetAppVersionName()//APP版本

            mViewModel.pushConfig(map)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 全局监听设备连接状态
     */
    private fun initDevice() {
        MrkDeviceManger.registerDeviceListener(mContext, object : DeviceListener() {
            override fun onConnectStatus(isAutoReconnect: Boolean, bean: DeviceMangerBean) {
                when (bean.connectEnum) {
                    DeviceConnectEnum.ON -> {
                        //绑定新设备送会员
                        if (bean.deviceDetails?.isNewBindDevice == 1) {
                            commonViewModel.checkActivity(onSuccess = {
                                StringManagerUtils.getString(
                                    StringKeyConstant.page_conectsuccess_toast, R.string.page_conectsuccess_toast
                                ).vbToast(isLong = true)
                            }, onError = {
                                StringManagerUtils.getString(
                                    StringKeyConstant.toast_device_connect_blue_success_tx,
                                    R.string.string_device_connect_success
                                ).vbToast()

                            })
                        } else {
                            StringManagerUtils.getString(
                                StringKeyConstant.toast_device_connect_blue_success_tx,
                                R.string.string_device_connect_success
                            ).vbToast()
                        }
                    }

                    DeviceConnectEnum.OFF -> {
                    }

                    DeviceConnectEnum.ING -> {
                    }

                    DeviceConnectEnum.ERROR -> {
                    }
                }
            }
        })
    }

    private fun showGuide360Version() {
        if (MMKV.defaultMMKV().getBoolean(AppConstant.MMKV_TIP_DATACENTER_GUIDE, true)) {
            MMKV.defaultMMKV().putBoolean(AppConstant.MMKV_TIP_DATACENTER_GUIDE, false)
            DataCenterDialog(this).setGoTrainListener {
                mDataBinding.viewPager.currentItem = 1
            }.show()
        }
    }

}