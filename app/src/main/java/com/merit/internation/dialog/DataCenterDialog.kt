package com.merit.internation.dialog

import android.content.Context
import android.view.View
import com.merit.internation.databinding.DialogDatacenterGuideBinding
import com.v.base.dialog.VBDialog

class DataCenterDialog(context: Context) : VBDialog<DialogDatacenterGuideBinding>(context), View.OnClickListener {

    private var listener: (() -> Unit)? = null

    fun setGoTrainListener(listener: (() -> Unit)): DataCenterDialog {
        this.listener = listener
        return this
    }

    override fun initData() {
        mDataBinding.v = this
    }


    override fun onClick(v: View) {
        if (v.id == mDataBinding.goTrainTv.id) {
            dismiss()
            listener?.invoke()
        }
    }
}