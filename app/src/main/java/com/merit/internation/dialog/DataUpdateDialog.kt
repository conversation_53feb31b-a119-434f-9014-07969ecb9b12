package com.merit.internation.dialog

import android.content.Context
import android.view.View
import com.merit.internation.databinding.DialogDataUpdateBinding
import com.v.base.annotaion.VBDialogOrientation
import com.v.base.dialog.VBDialog

class DataUpdateDialog(mContext: Context) :
    VBDialog<DialogDataUpdateBinding>(mContext),
    View.OnClickListener {
    private var listener: ((dialog: DataUpdateDialog) -> Unit)? = null


    fun setClickListener(listener: ((dialog: DataUpdateDialog) -> Unit)): DataUpdateDialog {
        this.listener = listener
        return this
    }


    override fun useDim(): Boolean {
        return false
    }

    override fun useDirection(): VBDialogOrientation {
        return VBDialogOrientation.TOP
    }

    override fun initData() {
        mDataBinding.v = this
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            mDataBinding.tvLook.id -> {
                dismiss()
                listener?.invoke(this)
            }

        }
    }

}