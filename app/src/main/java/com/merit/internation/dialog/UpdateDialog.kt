package com.merit.internation.dialog

import android.content.Context
import android.view.View
import com.merit.internation.bean.SplashBean
import com.merit.internation.databinding.DialogUpdateBinding
import com.v.base.dialog.VBDialog

/**
 * author  : ww
 * desc    : 版本升级
 * time    : 2022-08-01 14:27:44
 */
class UpdateDialog(mContext: Context, private val bean: SplashBean) :
    VBDialog<DialogUpdateBinding>(mContext),
    View.OnClickListener {

    private var listener: ((dialog: UpdateDialog, position: Int) -> Unit)? = null

    fun setClickListener(listener: ((dialog: UpdateDialog, position: Int) -> Unit)): UpdateDialog {
        this.listener = listener
        return this
    }

    override fun initData() {
        mDataBinding.v = this
        setDialogCancelable(false)

        if (!bean.remark.isNullOrEmpty()) {
            mDataBinding.tvContent.text = bean.remark
        }
        //type 1选择更新
        if (bean.type == 1) {
            mDataBinding.tvCancel.visibility = View.VISIBLE
        }

    }


    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.tvCancel.id -> {
                dismiss()
                listener?.invoke(this, 0)
            }

            mDataBinding.tvConfirm.id -> {
                dismiss()
                listener?.invoke(this, 1)
            }
        }
    }

}