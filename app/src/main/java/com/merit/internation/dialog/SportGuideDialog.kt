package com.merit.internation.dialog

import android.content.Context
import android.os.Build
import android.view.View
import android.webkit.CookieManager
import android.webkit.CookieSyncManager
import com.merit.internation.databinding.DialogSportGuideBinding
import com.merit.me.databinding.MDialogCacheManagementBinding
import com.v.base.dialog.VBDialog
import java.io.File
class SportGuideDialog(context: Context) : VBDialog<DialogSportGuideBinding>(context), View.OnClickListener {

    private var listener: ((dialog: SportGuideDialog, position: Int) -> Unit)? = null

    fun setClickListener(listener: ((dialog: SportGuideDialog, position: Int) -> Unit)): SportGuideDialog {
        this.listener = listener
        return this
    }

    override fun initData() {
        mDataBinding.v = this
    }


    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.tvLeft.id -> {
                dismiss()
                listener?.invoke(this, 0)
            }

            mDataBinding.tvRight.id -> {
                dismiss()
                listener?.invoke(this, 1)
            }
        }
    }
}