plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}
apply from: "${rootDir.getAbsolutePath()}/gradle/library.gradle"
android {
    compileSdkVersion MyVersions.compileSdkVersion

    defaultConfig {
        minSdkVersion MyVersions.minSdkVersion
        targetSdkVersion MyVersions.targetSdkVersion

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
    }

    buildTypes {
        release {
            buildConfigField "boolean", "IS_DEBUG", "false"//正式模式
            consumerProguardFiles 'proguard-rules.pro'
        }

        debug {
            buildConfigField "boolean", "IS_DEBUG", "true"//测试模式
            consumerProguardFiles 'proguard-rules.pro'
        }
    }

    buildFeatures {
        dataBinding = true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    MyLibs.dependencies.each { v -> api v }
    MyLibs.commonLib.each { v -> api project(v) }
    api(platform("com.google.firebase:firebase-bom:32.3.1"))
    api("com.google.firebase:firebase-analytics-ktx")
    releaseApi("com.google.firebase:firebase-crashlytics-ktx")
    api project(':moduleOther:pickerview')
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.3'
}
