<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="key"
            type="com.merit.common.StringKeyConstant" />

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/base_no_content" />

        <TextView
            vb_type_face_num="@{2}"
            android:id="@+id/tvMsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            vb_text_str="@{key.glo_noData_tx}"
            vb_text_id="@{@string/vb_string_temporarily_no_data}"
            android:textColor="#ff999999"
            android:textSize="14sp" />

        <TextView
            vb_type_face_num="@{4}"
            android:id="@+id/tvSubmit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="26dp"
            android:paddingLeft="25dp"
            android:paddingTop="8dp"
            android:paddingRight="25dp"
            android:paddingBottom="8dp"
            android:textColor="#ffffffff"
            android:textSize="14sp"
            android:visibility="gone"
            app:bl_corners_radius="8dp"
            app:bl_solid_color="#FF2451"
            tools:background="#FF2451"
            tools:text="查看更多课程"
            tools:visibility="visible" />
    </LinearLayout>
</layout>