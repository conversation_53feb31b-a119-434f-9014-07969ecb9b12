<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".dialog.ShareDialog">

    <data>

        <import type="com.merit.common.R" />

        <variable
            name="bean"
            type="com.merit.common.bean.ShareBean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingTop="14dp"
        android:paddingBottom="14dp">

        <ImageView
            android:layout_width="50dp"
            android:layout_height="50dp"
            vb_img_url="@{bean.icon}"
            tools:background="@android:color/black" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="10dp"
            android:text="@{bean.title}"
            android:textColor="@{bean.titleColor}"
            android:textSize="12.5sp"
            tools:text="titl" />

    </LinearLayout>

</layout>