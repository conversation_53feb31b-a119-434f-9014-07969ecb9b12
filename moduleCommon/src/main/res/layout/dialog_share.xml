<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".dialog.ShareDialog">

    <data>

        <import type="com.merit.common.R" />

        <variable
            name="v"
            type="com.merit.common.dialog.ShareDialog" />

        <variable
            name="key"
            type="com.merit.common.StringKeyConstant" />

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/shape25_white"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:gravity="center"
                    vb_text_str="@{key.share_title}"
                    vb_text_id="@{@string/string_share_title}"
                    android:textColor="#333333"
                    android:textSize="16sp" />

                <ImageView
                    android:id="@+id/iv_close"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="6dp"
                    android:padding="10dp"
                    android:src="@mipmap/base_icon_close_black"
                    app:vb_click="@{v}" />
            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="60dp"
                android:overScrollMode="never"
                android:scrollbars="none" />

        </LinearLayout>
    </FrameLayout>
</layout>