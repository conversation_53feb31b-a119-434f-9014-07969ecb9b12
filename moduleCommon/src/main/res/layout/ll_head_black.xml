<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="85dp"
        android:gravity="bottom"
        tools:background="#352C24">

        <FrameLayout
            android:id="@+id/flLeftArrow"
            vb_finish="@{true}"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="14dp"
            android:paddingTop="44dp"
            android:paddingEnd="14dp"
            android:paddingBottom="1dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:adjustViewBounds="true"
                android:src="@mipmap/base_icon_back_white" />
        </FrameLayout>


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingTop="44dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/string_vip_records_title"
                android:textColor="#ffffffff"
                android:textSize="16sp" />

        </FrameLayout>

    </RelativeLayout>
</layout>

