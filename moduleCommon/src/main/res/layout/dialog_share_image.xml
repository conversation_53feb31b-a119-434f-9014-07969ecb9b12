<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="com.merit.common.dialog.ShareImageDialog">

    <data>

        <import type="com.merit.common.R" />

        <variable
            name="v"
            type="com.merit.common.dialog.ShareImageDialog" />

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black"
        android:orientation="vertical">


        <ImageView
            android:id="@+id/ivBg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:background="@android:color/black"
            android:scaleType="centerCrop" />


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            android:scrollbars="none">

            <LinearLayout
                android:id="@+id/layoutContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <ImageView
                    android:id="@+id/ivCover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="300dp"
                    android:adjustViewBounds="true"
                    android:scaleType="centerCrop" />
            </LinearLayout>


        </ScrollView>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="220dp"
            android:layout_alignParentBottom="true"
            android:orientation="vertical"
            app:bl_gradient_angle="90"
            app:bl_gradient_centerColor="#232221"
            app:bl_gradient_endColor="#00232221"
            app:bl_gradient_startColor="#232221"
            app:bl_gradient_useLevel="true"
            tools:background="#232221">


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_weight="1"
                android:overScrollMode="never"
                android:scrollbars="none" />

            <ImageView
                android:id="@+id/ivClose"
                vb_click="@{v}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:padding="30dp"
                android:src="@mipmap/base_icon_close" />
        </LinearLayout>

    </RelativeLayout>

</layout>