<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:paddingTop="40dp"
    android:paddingBottom="20dp"
    android:layout_height="wrap_content"
    android:gravity="center">


    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        tools:src="@mipmap/base_classics_header" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="@string/srl_header_release"
        android:textColor="#ffff2451"
        android:textSize="13sp" />
</LinearLayout>