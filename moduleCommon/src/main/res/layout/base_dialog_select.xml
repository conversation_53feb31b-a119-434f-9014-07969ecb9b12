<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="com.merit.common.dialog.SelectItemDialog">

    <data>

        <variable
            name="v"
            type="com.merit.common.dialog.SelectItemDialog" />

        <variable
            name="key"
            type="com.merit.common.StringKeyConstant" />

    </data>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="bottom"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:orientation="vertical"
            app:bl_corners_topRadius="12dp"
            app:bl_solid_color="@android:color/white">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="61dp">

                <TextView
                    android:id="@+id/tvTitle"
                    vb_text_bold="@{true}"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:textColor="#ff333333"
                    android:textSize="16sp"
                    tools:text="性别" />

                <TextView
                    android:id="@+id/tvCancel"
                    vb_click="@{v}"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    vb_text_str="@{key.pop_ota_update_cancel_bt}"
                    vb_text_id="@{@string/string_cancel}"
                    android:textColor="#ff666666"
                    android:textSize="14sp" />


            </RelativeLayout>

            <View style="@style/vb_line_horizontal" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fadingEdge="none"
                android:overScrollMode="never"
                android:paddingLeft="20dp"
                android:paddingRight="20dp" />


        </LinearLayout>


    </LinearLayout>
</layout>