<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="white">#FFFFFF</color>
    <color name="black">#000000</color>
    <!--边沿透明View-->
    <declare-styleable name="EdgeTransparentView">
        <attr name="edge_position">
            <flag name="top" value="0x01" />
            <flag name="bottom" value="0x02" />
            <flag name="left" value="0x08" />
            <flag name="right" value="0x04" />
        </attr>
        <attr name="edge_width" format="dimension" />
    </declare-styleable>
    <declare-styleable name="VertcalSralRulerView">
        <!--字体颜色-->
        <attr name="textcolor" format="color"></attr>
        <!--选中的字体颜色-->
        <attr name="textselectcolor" format="color"></attr>
        <!--刻度颜色-->
        <attr name="lincolor" format="color"></attr>
        <!--字体大小-->
        <attr name="textsize" format="dimension"></attr>
        <attr name="labeltextsize" format="dimension"></attr>
        <!--刻度宽度-->
        <attr name="linheight" format="dimension"></attr>
        <!--最大刻度长度-->
        <attr name="maxlinlenght" format="dimension"></attr>
        <!--居中刻度长度-->
        <attr name="middlinlenght" format="dimension"></attr>
        <!--最小刻度长度-->
        <attr name="minlinlenght" format="dimension"></attr>
        <!--文字距离刻度的间隔-->
        <attr name="textmarglin" format="dimension"></attr>
        <!--每个刻度的间隔-->
        <attr name="itemspacing" format="dimension"></attr>
        <!--最大值-->
        <attr name="maxvalue" format="float"></attr>
        <!--最小值-->
        <attr name="minvalue" format="float"></attr>
        <!--值的间隔-->
        <attr name="perspanvalue" format="integer"></attr>
        <!--默认选中值-->
        <attr name="defaultvalue" format="integer"></attr>
        <!--中间线-->
        <attr name="selectorLine" format="dimension"></attr>
        <attr name="linemagin" format="dimension" />
        <attr name="selectorImage" format="reference" />
        <attr name="normalText" format="string" />


    </declare-styleable>
</resources>