package com.merit.common

object StringKeyConstant {

    const val home_tab = "home_tab"
    const val data_tab = "data_tab"
    const val data_title = "data_title"
    const val data_sum_distance_title = "data_sum_distance_title"
    const val data_today_title = "data_today_title"
    const val data_calories_tx = "data_calories_tx"

    //列表中没有的数据
    const val string_device_ota_fail = "string_device_ota_fail"
    const val string_device_foreground_service = "string_device_foreground_service"
    const val string_device_ota_file_fail = "string_device_ota_file_fail"
    const val string_device_unbind_success = "string_device_unbind_success"
    const val string_auto_text = "string_auto_text"
    const val string_suggest_text = "string_suggest_text"
    const val string_auto_countdown = "string_auto_countdown"
    const val string_cant_receive_code_hint_email = "string_cant_receive_code_hint_email"
    const val string_distance_unit = "string_distance_unit"
    const val string_service_email = "string_service_email"
    const val string_feedback_success = "string_feedback_success"
    const val string_feedback_hint = "string_feedback_hint"
    const val string_device_explain = "string_device_explain"
    const val string_m_change_succeeded = "string_m_change_succeeded"
    const val string_m_not_collection = "string_m_not_collection"
    const val string_language_success = "string_language_success"
    const val string_bluetooth_open_ing = "string_bluetooth_open_ing"
    const val string_open_loc_service = "string_open_loc_service"
    const val string_d_target_min0 = "string_d_target_min0"
    const val string_share_save_success = "string_share_save_success"


    const val data_duration_tx = "data_duration_tx"
    const val data_distance_tx = "data_distance_tx"
    const val data_total_time_tx = "data_total_time_tx"
    const val data_sum_kcal_tx = "data_sum_kcal_tx"
    const val data_cum_title_tx = "data_cum_title_tx"
    const val data_exercise_times_tx = "data_exercise_times_tx"
    const val data_time_unit = "data_time_unit"
    const val data_minutes_unit = "data_minutes_unit"
    const val data_kcal_unit = "data_kcal_unit"
    const val string_d_target_select = "string_d_target_select"
    const val string_d_target_null = "string_d_target_null"
    const val string_p_advice_frequency = "string_p_advice_frequency"
    const val string_p_advice_frequency1 = "string_p_advice_frequency1"
    const val string_to_open_loc_service = "string_to_open_loc_service"

    const val data_time_tx = "data_time_tx"
    const val data_time_year_unit = "data_time_year_unit"
    const val data_time_month_unit = "data_time_month_unit"
    const val data_time_day_unit = "data_time_day_unit"
    const val data_time_hour_unit = "data_time_hour_unit"
    const val data_time_min_unit = "data_time_min_unit"
    const val data_time_sec_unit = "data_time_sec_unit"
    const val data_FB_tx = "data_FB_tx"

    const val data_height_metric_unit = "data_height_metric_unit"
    const val data_height_imperial_unit = "data_height_imperial_unit"
    const val data_weight_metric_unit = "data_weight_metric_unit"
    const val data_weight_imperial_unit = "data_weight_imperial_unit"
    const val data_train_time_tx = "data_train_time_tx"
    const val data_train_kcal_unit = "data_train_kcal_unit"
    const val data_train_rpm_unit = "data_train_rpm_unit"
    const val data_train_spm_unit = "data_train_spm_unit"
    const val data_train_slope_tx = "data_train_slope_tx"
    const val data_distance_imperial_unit = "data_distance_imperial_unit"
    const val data_distance_metric_unit = "data_distance_metric_unit"
    const val data_train_resistance_tx = "data_train_resistance_tx"
    const val data_train_speed_tx = "data_train_speed_tx"
    const val data_train_auto_speed_tx = "data_train_auto_speed_tx"
    const val data_train_auto_resistance_tx = "data_train_auto_resistance_tx"


    const val login_guide_birth_holder_tx = "login_guide_birth_holder_tx"
    const val data_sport_tx = "data_sport_tx"
    const val data_sport_title = "data_sport_title"
    const val data_sport_category_title = "data_sport_category_title"
    const val data_sport_overview_title = "data_sport_overview_title"
    const val data_sport_recond_title = "data_sport_recond_title"
    const val data_sport_sum_kcal_txunit = "data_sport_sum_kcal_txunit"
    const val data_show_all_tx = "data_show_all_tx"
    const val data_rank_title = "data_rank_title"
    const val data_rank_best_tx = "data_rank_best_tx"
    const val string_p_sport_finish = "string_p_sport_finish"

    const val string_suggest_slope = "string_suggest_slope"


    const val dialog_reason_title = "dialog_reason_title"

    const val course_tab = "course_tab"

    const val course_title = "course_title"
    const val course_scheme_st = "course_scheme_st"
    const val course_ultra_tx = "course_ultra_tx"
    const val course_ultra_small_tx = "course_ultra_small_tx"
    const val course_ultra_hot_tx = "course_ultra_hot_tx"
    const val course_virtual_st = "course_virtual_st"
    const val course_survive_tx = "course_survive_tx"
    const val course_fat_burn_tx = "course_fat_burn_tx"

    const val course_virtual_main_tip = "course_virtual_main_tip"
    const val course_recommed = "course_recommed"
    const val course_praticed_tx = "course_praticed_tx"
    const val course_praticed_now_tx = "course_praticed_now_tx"
    const val course_praticed_day_tx = "course_praticed_day_tx"
    const val course_praticed_days_tx = "course_praticed_days_tx"
    const val course_free_tx = "course_free_tx"

    const val course_filter_by_equipment_tx = "course_filter_by_equipment_tx"
    const val course_filter_by_coach_tx = "course_filter_by_coach_tx"
    const val course_filter_by_difficult_tx = "course_filter_by_difficult_tx"
    const val course_filter_by_category_tx = "course_filter_by_category_tx"

    const val course_detail_title1_tx = "course_detail_title1_tx"
    const val course_detail_title2_tx = "course_detail_title2_tx"
    const val course_detail_title3_tx = "course_detail_title3_tx"
    const val course_detail_title4_tx = "course_detail_title4_tx"
    const val course_detail_title5_tx = "course_detail_title5_tx"
    const val course_detail_read_rule_tx = "course_detail_read_rule_tx"
    const val course_detail_readed_rule_tx = "course_detail_readed_rule_tx"
    const val course_detail_rule_name_tx = "course_detail_rule_name_tx"
    const val course_detail_no_more_tx = "course_detail_no_more_tx"


    const val course_play_merit_tip = "course_play_merit_tip"
    const val course_play_again_tx = "course_play_again_tx"
    const val course_play_loss_net_tx = "course_play_loss_net_tx"
    const val course_play_no_net_tx = "course_play_no_net_tx"
    const val course_play_load_error_tx = "course_play_load_error_tx"
    const val course_play_mobile_net_tip = "course_play_mobile_net_tip"
    const val course_play_error_tip = "course_play_error_tip"
    const val course_detail_next_bt = "course_detail_next_bt"


    const val course_detail_know_bt = "course_detail_know_bt"
    const val course_detail_play_perpare_tip = "course_detail_play_perpare_tip"
    const val course_detail_play_done_tip = "course_detail_play_done_tip"
    const val course_detail_play_start_bt = "course_detail_play_start_bt"
    const val course_detail_play_continue_bt = "course_detail_play_continue_bt"

    const val main_tab = "main_tab"

    const val main_health_data_tx = "main_health_data_tx"
    const val main_height_tx = "main_height_tx"
    const val main_weight_tx = "main_weight_tx"
    const val main_BMI_tx = "main_BMI_tx"
    const val main_practiced_title = "main_practiced_title"

    const val main_collect_title = "main_collect_title"
    const val main_follow_title = "main_follow_title"
    const val main_feedback_title = "main_feedback_title"

    const val main_user_title = "main_user_title"
    const val main_user_avatar_tx = "main_user_avatar_tx"
    const val main_user_nickname_tx = "main_user_nickname_tx"
    const val main_user_gender_tx = "main_user_gender_tx"
    const val main_user_birthday_tx = "main_user_birthday_tx"
    const val main_user_rate_resting_tx = "main_user_rate_resting_tx"
    const val main_user_rate_max_tx = "main_user_rate_max_tx"
    const val main_user_rate_reserve_tx = "main_user_rate_reserve_tx"
    const val main_user_height_input_tf = "main_user_height_input_tf"
    const val main_user_weight_input_tf = "main_user_weight_input_tf"
    const val main_user_rateResting_input_tf = "main_user_rateResting_input_tf"
    const val main_user_rateMax_input_tf = "main_user_rateMax_input_tf"
    const val main_user_rateReserve_input_tf = "main_user_rateReserve_input_tf"

    const val main_setting_title = "main_setting_title"
    const val main_setting_account_title = "main_setting_account_title"
    const val main_setting_language_title = "main_setting_language_title"
    const val main_setting_cache_title = "main_setting_cache_title"
    const val main_setting_about_title = "main_setting_about_title"
    const val main_setting_about_version_title = "main_setting_about_version_title"
    const val main_facebook_title = "main_facebook_title"
    const val main_account_nobind_title = "main_account_nobind_title"
    const val main_account_bind_title = "main_account_bind_title"

    const val main_logout_bt = "main_logout_bt"
    const val main_logout_tip_tx = "main_logout_tip_tx"
    const val main_logout_exit_bt = "main_logout_exit_bt"

    const val main_switch_unit_title = "main_switch_unit_title"
    const val main_switch_unit_metric_tx = "main_switch_unit_metric_tx"
    const val main_switch_unit_imperial_tx = "main_switch_unit_imperial_tx"
    const val main_switch_unit_success_tx = "main_switch_unit_success_tx"
    const val main_logoff_title = "main_logoff_title"
    const val main_logoff_st = "main_logoff_st"
    const val main_logoff_tip = "main_logoff_tip"
    const val main_logoff_detail_msg = "main_logoff_detail_msg"
    const val main_logoff_protocol_tx = "main_logoff_protocol_tx"

    const val main_logoff_confirm_bt = "main_logoff_confirm_bt"
    const val main_logoff_verify_tip = "main_logoff_verify_tip"
    const val main_logoff_reconfirm_bt = "main_logoff_reconfirm_bt"
    const val main_logoff_pop_msg = "main_logoff_pop_msg"

    const val main_server_title = "main_server_title"
    const val main_server_us_bt = "main_server_us_bt"
    const val page_servers_europe = "page_servers_europe"
    const val main_server_jp_bt = "main_server_jp_bt"

    const val main_email_bind_tx = "main_email_bind_tx"
    const val main_email_send_tx = "main_email_send_tx"
    const val main_email_set_pwd_tx = "main_email_set_pwd_tx"
    const val main_email_confirm_tx = "main_email_confirm_tx"
    const val main_email_bind_success_tx = "main_email_bind_success_tx"
    const val main_email_title = "main_email_title"
    const val main_email_verify_tx = "main_email_verify_tx"
    const val main_email_change_tx = "main_email_change_tx"
    const val main_email_send_code_tx = "main_email_send_code_tx"
    const val main_email_change_input_tx = "main_email_change_input_tx"

    const val main_pwd_change_title = "main_pwd_change_title"
    const val main_pwd_origin_tx = "main_pwd_origin_tx"
    const val main_pwd_new_tx = "main_pwd_new_tx"
    const val main_pwd_confirm_tx = "main_pwd_confirm_tx"
    const val main_pwd_change_success_tx = "main_pwd_change_success_tx"

    const val main_language_switch_success_msg = "main_language_switch_success_msg"
    const val main_language_switch_failure_msg = "main_language_switch_failure_msg"
    const val main_language_Chinese_tx = "main_language_Chinese_tx"
    const val main_language_japanese_tx = "main_language_japanese_tx"
    const val main_language_English_tx = "main_language_English_tx"
    const val main_language_Français_tx = "main_language_Français_tx"
    const val main_language_ltaliano_tx = "main_language_ltaliano_tx"
    const val main_language_Deutsch_tx = "main_language_Deutsch_tx"
    const val main_language_Español_tx = "main_language_Español_tx"

    const val device_title = "device_title"
    const val device_select_connext_tx = "device_select_connext_tx"
    const val device_bind_view_bt = "device_bind_view_bt"
    const val device_bind_add_bt = "device_bind_add_bt"

    const val device_search_title = "device_search_title"
    const val device_search_new_bt = "device_search_new_bt"
    const val device_search_connect_bt = "device_search_connect_bt"
    const val device_search_disconnect_bt = "device_search_disconnect_bt"
    const val device_search_bind_bt = "device_search_bind_bt"
    const val device_search_unbind_bt = "device_search_unbind_bt"
    const val device_search_searching_tx = "device_search_searching_tx"
    const val device_search_empty_tx = "device_search_empty_tx"
    const val device_search_again_bt = "device_search_again_bt"


    const val device_detail_title = "device_detail_title"
    const val device_detail_type_tx = "device_detail_type_tx"
    const val device_detail_lastest_tx = "device_detail_lastest_tx"
    const val device_detail_ota_update_title = "device_detail_ota_update_title"
    const val device_detail_ota_update_bt = "device_detail_ota_update_bt"
    const val device_detail_bind_bt = "device_detail_bind_bt"
    const val device_detail_connected_tx = "device_detail_connected_tx"
    const val device_detail_disconnected_tx = "device_detail_disconnected_tx"


    const val device_ota_title = "device_ota_title"
    const val device_ota_status_update_tx = "device_ota_status_update_tx"
    const val device_ota_status_success_tx = "device_ota_status_success_tx"
    const val device_ota_update_log_tx = "device_ota_update_log_tx"
    const val device_ota_version_tx = "device_ota_version_tx"


    const val device_type_title = "device_type_title"
    const val device_exercycles_tx = "device_exercycles_tx"
    const val device_rowers_tx = "device_rowers_tx"
    const val device_treadmils_tx = "device_treadmils_tx"
    const val device_eilipticals_tx = "device_eilipticals_tx"
    const val device_jmq_tx = "device_jmq_tx"
    const val device_skip_tx = "device_skip_tx"
    const val device_hoop_tx = "device_hoop_tx"
    const val device_small_tx = "device_small_tx"

    const val train_free_title = "train_free_title"
    const val train_distance_title = "train_distance_title"
    const val train_time_title = "train_time_title"

    const val train_set_customize_bt = "train_set_customize_bt"
    const val train_set_distance_title = "train_set_distance_title"
    const val train_set_distance_marhalf_tx = "train_set_distance_marhalf_tx"
    const val train_set_distance_mar_tx = "train_set_distance_mar_tx"
    const val train_set_distance_choose_metric_tx = "train_set_distance_choose_metric_tx"
    const val train_set_distance_choose_imperial_tx = "train_set_distance_choose_imperial_tx"
    const val train_set_distance_customize_input_tip = "train_set_distance_customize_input_tip"
    const val train_set_skip_tx = "train_set_skip_tx"
    const val train_set_skip_choose_tx = "train_set_skip_choose_tx"
    const val train_set_skip_unit = "train_set_skip_unit"
    const val train_set_skip_tip = "train_set_skip_tip"
    const val train_set_hoop_tx = "train_set_hoop_tx"
    const val train_set_hoop_choose_tx = "train_set_hoop_choose_tx"
    const val train_set_hoop_unit = "train_set_hoop_unit"
    const val train_set_hoop_tip = "train_set_hoop_tip"
    const val train_set_time_title = "train_set_time_title"
    const val train_set_time_choose_tx = "train_set_time_choose_tx"
    const val train_set_start_bt = "train_set_start_bt"

    const val train_advice_tx = "train_advice_tx"
    const val train_target_tx = "train_advice_tx"
    const val train_device_tx = "train_device_tx"
    const val train_time_duration_tx = "train_time_duration_tx"
    const val train_gear_tx = "train_gear_tx"
    const val train_sum_days_tx = "train_sum_days_tx"
    const val train_sum_kcal_tx = "train_sum_kcal_tx"
    const val train_speed_max_metric_txunit = "train_speed_max_metric_txunit"
    const val train_speed_max_imperial_txunit = "train_speed_max_imperial_txunit"
    const val train_speed_metric_txunit = "train_speed_metric_txunit"
    const val train_speed_imperial_txunit = "train_speed_imperial_txunit"

    const val train_oars_total_tx = "train_oars_total_tx"
    const val train_boat_spm_max_txunit = "train_boat_spm_max_txunit"
    const val train_boat_spm_txunit = "train_boat_spm_txunit"
    const val train_boat_spm_tx = "train_boat_spm_tx"
    const val train_rpm_max_txunit = "train_rpm_max_txunit"
    const val train_rpm_txunit = "train_rpm_txunit"
    const val train_rpm_tx = "train_rpm_tx"
    const val train_bicycle_total_steps_tx = "train_bicycle_total_steps_tx"
    const val train_slope_tx = "train_slope_tx"
    const val train_speed_skip_count_tx = "train_speed_skip_count_tx"
    const val train_speed_skip_count_txunit = "train_speed_skip_count_txunit"
    const val train_total_hoop_tx = "train_total_hoop_tx"
    const val train_speed_hoop_count_txunit = "train_speed_hoop_count_txunit"

    const val train_bpm_txunit = "train_bpm_txunit"
    const val train_kcal_txunit = "train_kcal_txunit"
    const val train_time_tx = "train_time_tx"
    const val train_time_txunit = "train_time_txunit"
    const val train_distance_metric_txunit = "train_distance_metric_txunit"
    const val train_distance_imperial_txunit = "train_distance_imperial_txunit"
    const val train_resistance_tx = "train_resistance_tx"
    const val train_speed_tx = "train_speed_tx"
    const val train_elertic_tx = "train_elertic_tx"
    const val train_difficulty_tx = "train_difficulty_tx"
    const val train_sug_rpm_tx = "train_sug_rpm_tx"
    const val train_sug_spm_tx = "train_sug_spm_tx"
    const val train_sug_speed_tx = "train_sug_speed_tx"

    const val train_manual_speed_tx = "train_manual_speed_tx"
    const val train_automatic_speed_tx = "train_automatic_speed_tx"
    const val train_manual_resistance_tx = "train_manual_resistance_tx"
    const val train_automatic_resistance_tx = "train_automatic_resistance_tx"
    const val train_completed_tx = "train_completed_tx"
    const val train_start_bt = "train_start_bt"
    const val train_know_bt = "train_know_bt"
    const val train_next_bt = "train_next_bt"
    const val train_continue_train_bt = "train_continue_train_bt"
    const val train_finish_report_bt = "train_finish_report_bt"
    const val train_end_train_bt = "train_end_train_bt"
    const val train_continue_bt = "train_continue_bt"
    const val train_pause_bt = "train_pause_bt"
    const val train_end_bt = "train_end_bt"

    const val login_confirm_btn = "login_confirm_btn"
    const val login_login_bt = "login_login_bt"
    const val login_regiset_bt = "login_regiset_bt"
    const val login_goto_login_bt = "login_goto_login_bt"
    const val login_third_tx = "login_third_tx"
    const val login_next_bt = "login_next_bt"
    const val login_email_tf = "login_email_tf"
    const val login_password_set_tf = "login_password_set_tf"
    const val login_password_tf = "login_password_tf"
    const val login_password_confirm_tf = "login_password_confirm_tf"

    const val login_vcode_tf = "login_vcode_tf"
    const val login_vcode_bt = "login_vcode_bt"
    const val login_sent_tx = "login_sent_tx"
    const val login_vcode_get_success_tx = "login_vcode_get_success_tx"
    const val login_forget_bt = "login_forget_bt"
    const val login_password_change_success_tx = "login_password_change_success_tx"


    const val login_protocol_pop_click_tx = "login_protocol_pop_click_tx"
    const val login_protocol_agree_tx = "login_protocol_agree_tx"
    const val login_guide_welcome_tx = "login_guide_welcome_tx"
    const val login_guide_name_tip = "login_guide_name_tip"
    const val login_guide_name_holder_tx = "login_guide_name_holder_tx"
    const val login_guide_gender_tip = "login_guide_gender_tip"
    const val login_guide_avater_tip = "login_guide_avater_tip"
    const val login_guide_birth_tip = "login_guide_birth_tip"
    const val login_guide_brith_holder_tx = "login_guide_brith_holder_tx"
    const val login_guide_height_tip = "login_guide_height_tip"
    const val login_guide_gender_male_bt = "login_guide_gender_male_bt"
    const val login_guide_gender_female_bt = "login_guide_gender_female_bt"
    const val login_guide_gender_other_bt = "login_guide_gender_other_bt"

    const val pop_picture_take_tx = "pop_picture_take_tx"
    const val pop_picture_select_tx = "pop_picture_select_tx"
    const val pop_picture_avatar_tx = "pop_picture_avatar_tx"

    const val pop_protocol_title = "pop_protocol_title"
    const val pop_protocol_msg = "pop_protocol_msg"
    const val pop_protocol_sure_bt = "pop_protocol_sure_bt"
    const val pop_agreement_title = "pop_agreement_title"
    const val pop_agreement_detail_tx = "pop_agreement_detail_tx"

    const val pop_agreement_privacy_tx = "pop_agreement_privacy_tx"
    const val pop_agreement_exit_bt = "pop_agreement_exit_bt"

    const val pop_version_update_tx = "pop_version_update_tx"
    const val pop_version_update_msg = "pop_version_update_msg"
    const val pop_version_update_cancel_bt = "pop_version_update_cancel_bt"
    const val pop_version_update_confirm_bt = "pop_version_update_confirm_bt"


    const val pop_facebook_bind_msg = "pop_facebook_bind_msg"
    const val pop_facebook_unbind_msg = "pop_facebook_unbind_msg"
    const val pop_facebook_unbind_cancel_bt = "pop_facebook_unbind_cancel_bt"
    const val pop_facebook_unbind_email_tx = "pop_facebook_unbind_email_tx"
    const val pop_facebook_bind_cancel_bt = "pop_facebook_bind_cancel_bt"


    const val pop_device_bind_msg = "pop_device_bind_msg"
    const val pop_device_select_msg = "pop_device_select_msg"
    const val pop_device_connect_bind_msg = "pop_device_connect_bind_msg"
    const val pop_device_bind_fail_msg = "pop_device_bind_fail_msg"
    const val pop_device_bind_fail_cancel_bt = "pop_device_bind_fail_cancel_bt"

    const val pop_device_bind_fail_confirm_bt = "pop_device_bind_fail_confirm_bt"
    const val pop_device_ota_updating_msg = "pop_device_ota_updating_msg"
    const val pop_device_blue_auth_msg = "pop_device_blue_auth_msg"
    const val pop_device_cancel_msg = "pop_device_cancel_msg"
    const val pop_device_connect_msg = "pop_device_connect_msg"
    const val pop_device_connect_cancel_bt = "pop_device_connect_cancel_bt"
    const val pop_device_connect_confirm_bt = "pop_device_connect_confirm_bt"

    const val pop_device_disconnect_msg = "pop_device_disconnect_msg"
    const val pop_device_unbind_msg = "pop_device_unbind_msg"
    const val pop_device_unbind_confirm_bt = "pop_device_unbind_confirm_bt"
    const val pop_device_unbind_cancel_bt = "pop_device_unbind_cancel_bt"

    const val pop_login_no_code_title = "pop_login_no_code_title"
    const val pop_login_no_code_msg = "pop_login_no_code_msg"
    const val pop_feeback_msg = "pop_feeback_msg"
    const val pop_user_edit_nickname_title = "pop_user_edit_nickname_title"
    const val pop_user_edit_nickname_msg = "pop_user_edit_nickname_msg"


    const val pop_user_edit_nickname_tf = "pop_user_edit_nickname_tf"
    const val pop_user_clear_cache_title = "pop_user_clear_cache_title"
    const val pop_user_clear_cache_msg = "pop_user_clear_cache_msg"

    const val pop_train_merit_msg = "pop_train_merit_msg"
    const val pop_train_disconnect_title = "pop_train_disconnect_title"
    const val pop_train_disconnect_msg = "pop_train_disconnect_msg"
    const val pop_train_disconnect_cancel_bt = "pop_train_disconnect_cancel_bt"

    const val pop_train_before_save_msg = "pop_train_before_save_msg"
    const val pop_train_before_save_bt = "pop_train_before_save_bt"

    const val pop_title = "pop_title"
    const val pop_ota_update_msg = "pop_ota_update_msg"
    const val pop_ota_update_cancel_bt = "pop_ota_update_cancel_bt"
    const val pop_ota_updating_exit_msg = "pop_ota_updating_exit_msg"
    const val pop_ota_updating_exit_confirm_bt = "pop_ota_updating_exit_confirm_bt"

    const val pop_ota_updating_exit_cancel_bt = "pop_ota_updating_exit_cancel_bt"

    const val pop_course_play_exit_msg = "pop_course_play_exit_msg"
    const val pop_course_play_cancel_bt = "pop_course_play_cancel_bt"
    const val pop_course_play_begin_confirm_btn = "pop_course_play_begin_confirm_btn"
    const val pop_course_play_begin_cancel_btn = "pop_course_play_begin_cancel_btn"
    const val pop_course_play_exit_title = "pop_course_play_exit_title"
    const val pop_course_play_exit_feed_msg = "pop_course_play_exit_feed_msg"

    const val toast_net_failer_tx = "toast_net_failer_tx"
    const val toast_email_copy_success_tx = "toast_email_copy_success_tx"
    const val toast_course_detail_vip_tx = "toast_course_detail_vip_tx"
    const val toast_coach_unfollow_tx = "toast_coach_unfollow_tx"

    const val toast_login_facebook_failer_tx = "toast_login_facebook_failer_tx"
    const val toast_login_facebook_cancel_tx = "toast_login_facebook_cancel_tx"
    const val toast_login_apple_failer_tx = "toast_login_apple_failer_tx"
    const val toast_login_iOS13_failer_tx = "toast_login_iOS13_failer_tx"
    const val toast_login_password_rules_tx = "toast_login_password_rules_tx"
    const val toast_input_password_failure_tx = "toast_input_password_failure_tx"
    const val page_login_passport_tips = "page_login_passport_tips"


    const val toast_login_agreement_tx = "toast_login_agreement_tx"
    const val toast_login_password_error_tx = "toast_login_password_error_tx"
    const val toast_user_edit_nickname_empty_tx = "toast_user_edit_nickname_empty_tx"
    const val toast_user_edit_nickname_rules_tx = "toast_user_edit_nickname_rules_tx"

    const val toast_camera_authority_tx = "toast_camera_authority_tx"
    const val toast_img_save_failure_tx = "toast_img_save_failure_tx"
    const val toast_img_save_success_tx = "toast_img_save_success_tx"

    const val toast_train_drag_max_tx = "toast_train_drag_max_tx"
    const val toast_train_drag_min_tx = "toast_train_drag_min_tx"
    const val toast_train_speed_max_tx = "toast_train_speed_max_tx"
    const val toast_train_speed_min_tx = "toast_train_speed_min_tx"
    const val toast_train_slope_max_tx = "toast_train_slope_max_tx"
    const val toast_train_slope_min_tx = "toast_train_slope_min_tx"
    const val toast_train_set_time_min_tx = "toast_train_set_time_min_tx"
    const val toast_train_set_distance_max_metric_tx = "toast_train_set_distance_max_metric_tx"
    const val toast_train_set_distance_max_imperial_tx = "toast_train_set_distance_max_imperial_tx"
    const val toast_train_treadmill_open_tx = "toast_train_treadmill_open_tx"

    const val toast_device_connect_failure_tx = "toast_device_connect_failure_tx"
    const val toast_device_connect_tx = "toast_device_connect_tx"
    const val toast_device_disconned_tx = "toast_device_disconned_tx"
    const val toast_device_slow_down_tx = "toast_device_slow_down_tx"
    const val toast_device_connecting_tx = "toast_device_connecting_tx"
    const val toast_device_open_blue_tx = "toast_device_open_blue_tx"
    const val toast_device_connect_blue_failure_tx = "toast_device_connect_blue_failure_tx"
    const val toast_device_connect_blue_success_tx = "toast_device_connect_blue_success_tx"
    const val toast_device_reconnect_tx = "toast_device_reconnect_tx"

    const val toast_ota_updating_wating_tx = "toast_ota_updating_wating_tx"
    const val toast_ota_update_failure_tx = "toast_ota_update_failure_tx"
    const val toast_ota_update_failure_again_tx = "toast_ota_update_failure_again_tx"
    const val toast_ota_update_success_tx = "toast_ota_update_success_tx"
    const val toast_ota_blue_disconnect_tx = "toast_ota_blue_disconnect_tx"
    const val toast_ota_update_success_reconnect_tx = "toast_ota_update_success_reconnect_tx"

    const val toast_course_play_auto_turn_off_tx = "toast_course_play_auto_turn_off_tx"
    const val toast_course_play_auto_turn_on_tx = "toast_course_play_auto_turn_on_tx"
    const val toast_course_play_4g_tx = "toast_course_play_4g_tx"
    const val toast_course_play_before_end_tx = "toast_course_play_before_end_tx"
    const val toast_course_play_connected_tx = "toast_course_play_connected_tx"
    const val toast_course_play_danmu_tx = "toast_course_play_danmu_tx"

    const val glo_netError_tx = "glo_netError_tx"
    const val glo_noData_tx = "glo_noData_tx"
    const val glo_noNet_tx = "glo_noNet_tx"
    const val glo_noData_refresh_bt = "glo_noData_refresh_bt"
    const val glo_noData_reload_bt = "glo_noData_reload_bt"
    const val glo_noData_coach_tx = "glo_noData_coach_tx"

    const val glo_noData_collect_tx = "glo_noData_collect_tx"
    const val glo_noData_device_tx = "glo_noData_device_tx"
    const val glo_noData_course_tx = "glo_noData_course_tx"
    const val glo_noData_addDevice_bt = "glo_noData_addDevice_bt"
    const val glo_noData_plan_tx = "glo_noData_plan_tx"
    const val glo_noData_live_tx = "glo_noData_live_tx"
    const val glo_noData_message_tx = "glo_noData_message_tx"
    const val glo_noData_ab_course_tx = "glo_noData_ab_course_tx"
    const val glo_noData_course_delist_tx = "glo_noData_course_delist_tx"
    const val glo_noSet_tx = "glo_noSet_tx"


    const val glo_submit_bt = "glo_submit_bt"
    const val glo_more_bt = "glo_more_bt"
    const val glo_expand_bt = "glo_expand_bt"
    const val glo_collapse_bt = "glo_collapse_bt"
    const val glo_skip_bt = "glo_skip_bt"
    const val glo_filter_bt = "glo_filter_bt"
    const val glo_reset_bt = "glo_reset_bt"
    const val glo_done_bt = "glo_done_bt"
    const val glo_next_bt = "glo_next_bt"
    const val glo_save_bt = "glo_save_bt"
    const val glo_back_bt = "glo_back_bt"
    const val glo_clear_bt = "glo_clear_bt"

    const val glo_confirm_bt = "glo_confirm_bt"
    const val glo_cancel_bt = "glo_cancel_bt"

    const val glo_oneDecimal_tx = "glo_oneDecimal_tx"
    const val glo_followed_tx = "glo_followed_tx"
    const val glo_follow_tx = "glo_follow_tx"
    const val glo_loading_tx = "glo_loading_tx"

    const val refresh_pull_down_tx = "refresh_pull_down_tx"
    const val refresh_release_tx = "refresh_release_tx"
    const val refresh_pull_up_tx = "refresh_pull_up_tx"
    const val refresh_more_tx = "refresh_more_tx"
    const val refresh_loadding_tx = "refresh_loadding_tx"
    const val refresh_no_data_tx = "refresh_no_data_tx"

    const val share_success_tx = "share_success_tx"
    const val share_failure_tx = "share_failure_tx"
    const val share_cancel_tx = "share_cancel_tx"
    const val share_uninstalled_tx = "share_uninstalled_tx"
    const val share_title = "share_title"

    //新增实景竞技字符串 --start

    const val home_video_challenge_title = "home_video_challenge_title"
    const val video_detail_join_challenge_tx = "video_detail_join_challenge_tx"
    const val video_detail_kcal_title = "video_detail_kcal_title"
    const val video_detail_distance_km_title = "video_detail_distance_km_title"

    const val video_detail_distance_mi_title = "video_detail_distance_mi_title"
    const val video_detail_duration_title = "video_detail_duration_title"
    const val video_detail_hot_title = "video_detail_hot_title"
    const val video_detail_hot_tip_tx = "video_detail_hot_tip_tx"
    const val video_detail_pop_detail_title = "video_detail_pop_detail_title"
    const val video_detail_pop_intro_tx = "video_detail_pop_intro_tx"
    const val video_detail_pop_intro_more_bt = "video_detail_pop_intro_more_bt"
    const val video_detail_pop_equipment_allowed_tx = "video_detail_pop_equipment_allowed_tx"
    const val video_challenge_rank_title = "video_challenge_rank_title"
    const val video_challenge_result_title = "video_challenge_result_title"
    const val video_challenge_result_seeReport_bt = "video_challenge_result_seeReport_bt"

    const val video_challenge_result_success_tip = "video_challenge_result_success_tip"
    const val video_challenge_result_failure_tip = "video_challenge_result_failure_tip"
    const val mine_challenge_title = "mine_challenge_title"
    const val mine_challenge_rankboard_title = "mine_challenge_rankboard_title"

    const val mine_challenge_record_title = "mine_challenge_record_title"
    const val mine_challenge_course_tx = "mine_challenge_course_tx"
    const val mine_challenge_challenged_tx = "mine_challenge_challenged_tx"
    const val mine_challenge_rank_tx = "mine_challenge_rank_tx"
    const val mine_challenge_record_success_tx = "mine_challenge_record_success_tx"
    const val mine_challenge_record_faild_tx = "mine_challenge_record_faild_tx"
    const val video_challenge_seeAll_bt = "video_challenge_seeAll_bt"
    const val video_challenge_challenged_tx = "video_challenge_challenged_tx"
    const val video_challenge_challenge_bt = "video_challenge_challenge_bt"
    const val pop_video_challenge_device_connect_msg = "pop_video_challenge_device_connect_msg"
    const val data_train_power_tx = "data_train_power_tx"
    const val data_train_hot_tx = "data_train_hot_tx"
    const val data_train_distance_tx = "data_train_distance_tx"
    const val data_train_caloretion_tx = "data_train_caloretion_tx"
    const val video_ready_exit_bt = "video_ready_exit_bt"
    const val video_ready_public_tx = "video_ready_public_tx"
    const val video_ready_begin_tx = "video_ready_begin_tx"
    const val video_challenge_success_msg = "video_challenge_success_msg"
    const val video_challenge_vs_board_tx = "video_challenge_vs_board_tx"
    const val video_challenge_vs_board_total_tx = "video_challenge_vs_board_total_tx"

    const val mine_my_workout_data_tip = "mine_my_workout_data_tip"
    const val mine_my_workout_data_new_record = "mine_my_workout_data_new_record"
    const val mine_my_workout_data_pld_record = "mine_my_workout_data_pld_record"
    const val pop_compensate_success_check_bt = "pop_compensate_success_check_bt"
    const val pop_compensate_success_tx = "pop_compensate_success_tx"
    const val course_ultra_title = "course_ultra_title"
    const val course_ultra_introduce_tx = "course_ultra_introduce_tx"
    //实景竞技--end


    //实时赛 start
    const val home_online_sport_title = "home_online_sport_title"
    const val home_online_sport_time_tx = "home_online_sport_time_tx"
    const val home_online_sport_local_time_tx = "home_online_sport_local_time_tx"
    const val home_online_nostart_state_tx = "home_online_nostart_state_tx"
    const val home_online_going_state_tx = "home_online_going_state_tx"
    const val home_online_end_state_tx = "home_online_end_state_tx"
    const val home_online_nostart_countdown_tx = "home_online_nostart_countdown_tx"
    const val home_online_going_countdown_tx = "home_online_going_countdown_tx"
    const val home_online_end_join_num_tx = "home_online_end_join_num_tx"
    const val home_online_nostart_tip_tx = "home_online_nostart_tip_tx"
    const val home_online_end_tip_tx = "home_online_end_tip_tx"
    const val home_online_going_bt = "home_online_going_bt"
    const val home_online_end_challenge_bt = "home_online_end_challenge_bt"
    const val home_online_end_leaderboard_bt = "home_online_end_leaderboard_bt"
    const val online_sport_title = "online_sport_title"
    const val online_sports_tip_tx = "online_sports_tip_tx"
    const val video_real_ready_begin_tx = "video_real_ready_begin_tx"
    const val video_real_ready_public_tx = "video_real_ready_public_tx"
    const val video_real_toast_hot_tx = "video_real_toast_hot_tx"
    const val video_real_toast_rank_tx = "video_real_toast_rank_tx"
    const val video_real_rank_lastRecord_tx = "video_real_rank_lastRecord_tx"
    const val video_real_rank_online_tx = "video_real_rank_online_tx"
    const val video_real_rank_title_tx = "video_real_rank_title_tx"
    const val video_real_toast_intoFail_tx = "video_real_toast_intoFail_tx"
    const val online_sport_result_title = "online_sport_result_title"
    const val online_sport_result_date_tx = "online_sport_result_date_tx"
    const val online_sport_result_participants_tx = "online_sport_result_participants_tx"
    const val online_sport_result_tip_tx = "online_sport_result_tip_tx"
    const val online_sport_result_start_again_bt = "online_sport_result_start_again_bt"
    const val online_sport_result_data_title = "online_sport_result_data_title"
    const val online_sport_result_data_record_bt = "online_sport_result_data_record_bt"
    const val online_sport_result_data_hot_tx = "online_sport_result_data_hot_tx"
    const val online_sport_result_data_ranking_tx = "online_sport_result_data_ranking_tx"
    const val online_sport_result_data_historical_rank_bt = "online_sport_result_data_historical_rank_bt"
    const val online_sport_result_data_tip_tx = "online_sport_result_data_tip_tx"
    const val online_sport_result_leaderboard_title = "online_sport_result_leaderboard_title"
    const val online_sport_result_leaderboard_bt = "online_sport_result_leaderboard_bt"
    const val online_sport_result_leaderboard_tip_tx = "online_sport_result_leaderboard_tip_tx"
    const val online_sport_historical_rank_title = "online_sport_historical_rank_title"
    const val online_sport_leaderboard_title = "online_sport_leaderboard_title"
    const val mine_challenge_rank_challenge_bt = "mine_challenge_rank_challenge_bt"
    const val mine_challenge_rank_online_bt = "mine_challenge_rank_online_bt"
    const val mine_challenge_date_tx = "mine_challenge_date_tx"
    const val online_sport_highpower_tx = "online_sport_highpower_tx"
    const val online_sport_time_tx = "online_sport_time_tx"
    const val pop_device_noConnect_msg = "pop_device_noConnect_msg"
    const val video_challenge_success_detail_msg = "video_challenge_success_detail_msg"
    const val my_equipment = "profile_main_myequip"
    const val myequip_list_title = "myequip_list_title"
    const val devsearch_main_checkstatus = "devsearch_main_checkstatus"
    const val devsearch_main_poweron = "devsearch_main_poweron"
    const val devsearch_fail_description = "devsearch_fail_description"
    const val devsearch_fail_checktips = "devsearch_fail_checktips"
    const val devsearch_fail_tips1 = "devsearch_fail_tips1"
    const val devsearch_fail_tips2 = "devsearch_fail_tips2"
    const val devsearch_fail_tips3 = "devsearch_fail_tips3"
    const val freetraining_mian_slope = "freetraining_mian_slope"

    //V3.0.0
    const val btn_data = "btn_data"
    const val btn_train = "btn_train"
    const val btn_profile = "btn_profile"
    const val btn_train_guide = "btn_train_guide"
    const val btn_data_equipment_connection = "btn_data_equipment_connection"
    const val btn_data_today_title = "btn_data_today_title"
    const val btn_data_today_calories = "btn_data_today_calories"
    const val btn_data_today_duration = "btn_data_today_duration"
    const val btn_data_today_distance = "btn_data_today_distance"
    const val btn_data_calendar_title = "btn_data_calendar_title"
    const val btn_data_calendar_viewmore = "btn_data_calendar_viewmore"
    const val btn_data_activitydata_title = "btn_data_activitydata_title"
    const val btn_data_activitydata_equipment_switch_allactivity = "btn_data_activitydata_equipment_switch_allactivity"
    const val btn_data_activitydata_equipment_switch_exercycles = "btn_data_activitydata_equipment_switch_exercycles"
    const val btn_data_activitydata_equipment_switch_rowers = "btn_data_activitydata_equipment_switch_rowers"
    const val btn_data_activitydata_equipment_switch_treadmills = "btn_data_activitydata_equipment_switch_treadmills"
    const val btn_data_activitydata_equipment_switch_ellipticals = "btn_data_activitydata_equipment_switch_ellipticals"
    const val btn_data_activitydata_timetype_switch_day = "btn_data_activitydata_timetype_switch_day"
    const val btn_data_activitydata_timetype_switch_week = "btn_data_activitydata_timetype_switch_week"
    const val btn_data_activitydata_timetype_switch_month = "btn_data_activitydata_timetype_switch_month"
    const val btn_data_activitydata_timetype_switch_year = "btn_data_activitydata_timetype_switch_year"
    const val btn_data_activitydata_timetype_switch_all = "btn_data_activitydata_timetype_switch_all"
    const val btn_data_activitydata_viewmore = "btn_data_activitydata_viewmore"
    const val btn_data_activitydata_calories = "btn_data_activitydata_calories"
    const val btn_data_activitydata_distance = "btn_data_activitydata_distance"
    const val btn_data_activitydata_duration = "btn_data_activitydata_duration"
    const val btn_data_activitydata_days = "btn_data_activitydata_days"
    const val btn_data_activitydata_times = "btn_data_activitydata_times"
    const val btn_data_activitydata_datatype_switch_calories = "btn_data_activitydata_datatype_switch_calories"
    const val btn_data_activitydata_datatype_switch_distance = "btn_data_activitydata_datatype_switch_distance"
    const val btn_data_activitydata_datatype_switch_duration = "btn_data_activitydata_datatype_switch_duration"
    const val page_calendar_title = "page_calendar_title"
    const val btn_train_title = "btn_train_title"
    const val btn_profile_contactus = "btn_profile_contactus"
    const val page_contactus_title = "page_contactus_title"
    const val page_contactus_emailaddress_title = "page_contactus_emailaddress_title"
    const val page_contactus_emailaddress = "page_contactus_emailaddress"
    const val page_contactus_issue_title = "page_contactus_issue_title"
    const val page_contactus_issue = "page_contactus_issue"
    const val page_contactus_document = "page_contactus_document"
    const val page_contactus_pictures_remind = "page_contactus_pictures_remind"
    const val page_contactus_video_remind = "page_contactus_video_remind"
    const val page_contactus_submitted_remind = "page_contactus_submitted_remind"


    //地图
    const val btn_train_mapmode_title = "btn_train_mapmode_title"
    const val btn_train_mapmode_viewmore = "btn_train_mapmode_viewmore"
    const val btn_train_mapmode_guide_title = "btn_train_mapmode_guide_title"
    const val btn_train_mapmode_guide_btn = "btn_train_mapmode_guide_btn"


    //首页运动引导
    const val btn_data_guide_into_trainmode_popup_title = "btn_data_guide_into_trainmode_popup_title"
    const val btn_data_guide_into_trainmode_popup_illustrate = "btn_data_guide_into_trainmode_popup_illustrate"
    const val btn_data_guide_into_trainmode_popup_cancel = "btn_data_guide_into_trainmode_popup_cancel"
    const val btn_data_guide_into_trainmode_popup_enter = "btn_data_guide_into_trainmode_popup_enter"
    const val page_personal_change_avatar_restriction_toast = "page_personal_change_avatar_restriction_toast"

    //3.4.0
    const val btn_data_healthapp_guide = "btn_data_healthapp_guide";
    const val btn_profile_healthapp_guide = "btn_profile_healthapp_guide";
    const val btn_profile_healthapp = "btn_profile_healthapp";
    const val page_healthapp_setting_title = "page_healthapp_setting_title";
    const val page_healthapp_setting_notice_ios = "page_healthapp_setting_notice_ios";
    const val page_healthapp_setting_notice_android = "page_healthapp_setting_notice_android";
    const val page_healthapp_setting_notsupport_ios = "page_healthapp_setting_notsupport_ios";
    const val page_healthapp_setting_notsupport_android = "page_healthapp_setting_notsupport_android";
    const val page_healthapp_setting_notinstalled_android = "page_healthapp_setting_notinstalled_android";
    const val page_healthapp_setting_connect_btn = "page_healthapp_setting_connect_btn";
    const val page_healthapp_setting_edit_btn = "page_healthapp_setting_edit_btn";
    const val page_healthapp_setting_guide_title_ios = "page_healthapp_setting_guide_title_ios";
    const val page_healthapp_setting_guide_content_ios = "page_healthapp_setting_guide_content_ios";
    const val page_healthapp_setting_guide_btn_ios = "page_healthapp_setting_guide_btn_ios";
    const val page_healthapp_setting_qa_ios = "page_healthapp_setting_qa_ios";
    const val page_healthapp_setting_qa_andriod = "page_healthapp_setting_qa_andriod";
    const val page_device_connect_guide_btn = "page_device_connect_guide_btn";
    const val btn_trainning_tab_challenges = "btn_trainning_tab_challenges";
    const val btn_trainning_tab_coaching = "btn_trainning_tab_coaching";
    const val btn_trainning_tab_freetrain = "btn_trainning_tab_freetrain";
    const val page_healthapp_setting_notice_title_andriod = "page_healthapp_setting_notice_title_andriod";

    //力量站增加
    const val label_freetraining_times = "label_freetraining_times"
    const val btn_data_activitydata_equipment_switch_strengthtrainer =
        "btn_data_activitydata_equipment_switch_strengthtrainer"
    const val toast_training_unsupported = "toast_training_unsupported"
    const val label_coursetraining_times = "label_coursetraining_times"


    //    3.5
    const val btn_train_mapmode_3d_guide_title = "btn_train_mapmode_3d_guide_title"
    const val btn_train_mapmode_3d_guide_btn = "btn_train_mapmode_3d_guide_btn"
    const val btn_profile_subscribe_module_rights_btn = "btn_profile_subscribe_module_rights_btn"
    const val btn_profile_subscribe_module_subtitle = "btn_profile_subscribe_module_subtitle"
    const val btn_profile_subscribe_module_expiry = "btn_profile_subscribe_module_expiry"
    const val btn_profile_subscribe_module_vaild = "btn_profile_subscribe_module_vaild"


    const val page_activation_title: String = "page_activation_title"

    const val page_activation_subtitle: String = "page_activation_subtitle"

    const val page_activation_yes_btn: String = "page_activation_yes_btn"

    const val page_activation_no_btn: String = "page_activation_no_btn"

    const val page_activation_select_title: String = "page_activation_select_title"

    const val page_activation_select_prompt_title: String = "page_activation_select_prompt_title"

    const val page_activation_device_select_prompt_subtitle: String = "page_activation_device_select_prompt_subtitle"

    const val page_activation_device_select_rowing: String = "page_activation_device_select_rowing"

    const val page_activation_device_select_bike: String = "page_activation_device_select_bike"

    const val page_activation_device_select_ellipticals: String = "page_activation_device_select_ellipticals"

    const val page_activation_device_select_treadmill: String = "page_activation_device_select_treadmill"

    const val page_activation_device_select_strength: String = "page_activation_device_select_strength"

    const val page_activation_device_select_others: String = "page_activation_device_select_others"

    const val page_activation_device_select_next_btn: String = "page_activation_device_select_next_btn"

    const val page_activation_control_select_subtitle: String = "page_activation_control_select_subtitle"

    const val page_activation_control_select_shuttle: String = "page_activation_control_select_shuttle"

    const val page_activation_control_select_electronic: String = "page_activation_control_select_electronic"
    const val popup_unconnect_title: String = "popup_unconnect_title"
    const val popup_unconnect_subtitle1: String = "popup_unconnect_subtitle1"
    const val popup_unconnect_contactus_btn: String = "popup_unconnect_contactus_btn"
    const val popup_unconnect_try_btn: String = "popup_unconnect_try_btn"
    const val popup_unconnect_subtitle2: String = "popup_unconnect_subtitle2"
    const val page_data_updata_title: String = "page_data_updata_title"
    const val page_data_updata_go_btn: String = "page_data_updata_go_btn"

    //    3.8
    const val page_train_popup_title1: String = "page_train_popup_title1"
    const val page_train_popup_content1: String = "page_train_popup_content1"
    const val page_viralsharing_popup_confirm: String = "page_viralsharing_popup_confirm"
    const val btn_activation_freedevice: String = "btn_activation_freedevice"

    //3.8.1
    const val btn_activation_freedevice_label: String = "btn_activation_freedevice_label"
    const val page_conectsuccess_toast: String = "page_conectsuccess_toast"
    const val page_train_popup_freemembershipactivity: String = "page_train_popup_freemembershipactivity"

    //
    const val page_challenges_mapmode_new: String = "page_challenges_mapmode_new"


    const val profile_badgewall: String = "profile_badgewall"

}