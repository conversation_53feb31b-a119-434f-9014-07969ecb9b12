package com.merit.common

import android.app.Activity
import android.content.res.Configuration
import android.graphics.Color
import com.didi.drouter.api.DRouter
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.hjq.language.MultiLanguages
import com.hjq.language.OnLanguageListener
import com.merit.common.net.NetworkExceptionHandling
import com.merit.common.net.NetworkHeadInterceptor
import com.merit.common.utils.CrashHandler
import com.merit.common.utils.EnvUtil
import com.merit.common.utils.FlutterUtils
import com.merit.common.utils.StringManagerUtils
import com.merit.common.viewmodel.CommonViewModel
import com.merit.common.views.MyClassicsFooter
import com.merit.common.views.MyClassicsHeader
import com.mrk.common.CommonApplication
import com.mrk.network.MrkNetworkConfig
import com.mrk.network.net.MrkNetOptions
import com.tencent.mmkv.MMKV
import com.v.base.VBConfig
import com.v.base.VBConfigOptions
import com.v.base.utils.getApplicationViewModel
import com.v.log.LogConfig
import com.v.log.util.log
import me.jessyan.autosize.AutoSize
import me.jessyan.autosize.AutoSizeConfig
import me.jessyan.autosize.onAdaptListener
import me.jessyan.autosize.utils.ScreenUtils
import java.util.Locale
import java.util.concurrent.TimeUnit


val commonViewModel by lazy {
    MyApplication.mCommonViewModel
}

open class MyApplication : CommonApplication() {

    companion object {
        lateinit var instance: MyApplication
        lateinit var mCommonViewModel: CommonViewModel
        lateinit var firebaseAnalytics: FirebaseAnalytics
    }

    override fun logConfig(): LogConfig? {
        return LogConfig(this, BuildConfig.IS_DEBUG, false)
    }

    override fun onCreate() {
        super.onCreate()
        FirebaseApp.initializeApp(this)
        // Obtain the FirebaseAnalytics instance.
        firebaseAnalytics = Firebase.analytics
        EnvUtil.init()
    }


    override fun initData() {
        super.initData()
        instance = this
        StringManagerUtils.init(instance)
        val rootDir = MMKV.initialize(this)
        rootDir.log()

        DRouter.init(this)
        intAutoSize()

        CrashHandler.getInstance().init(applicationContext)


        val options =
            VBConfigOptions.Builder().setStatusBarColor("#ffffff").setToolbarTitleColor(Color.parseColor("#242424"))
                .setRecyclerViewEmptyLayout(R.layout.view_list_empty).setSmartRefreshHeader { context, _ ->
                    MyClassicsHeader(context)
                }.setSmartRefreshFooter { context, _ ->
                    MyClassicsFooter(context)
                }.build()
        VBConfig.init(options)

        //网络请求配置
        val netOptions =
            MrkNetOptions.Builder().setBaseUrl(AppConstant.getBaseUrl()).setInterceptor(NetworkHeadInterceptor())
                .setExceptionHandling(NetworkExceptionHandling())
//                .setSslSocketFactoryInputStream(this.assets.open("merach.com.pem"))
                .build()
        netOptions.okHttpClient.apply {
            connectTimeout(30, TimeUnit.SECONDS)   //超时时间 连接、读、写
        }
        MrkNetworkConfig.init(netOptions)

        mCommonViewModel = getApplicationViewModel(
            this, CommonViewModel::class.java
        )
        FlutterUtils.init(this)

        // 设置语种变化监听器
        MultiLanguages.setOnLanguageListener(object : OnLanguageListener {
            override fun onAppLocaleChange(oldLocale: Locale, newLocale: Locale) {
                ("监听到应用切换了语种，旧语种：$oldLocale，新语种：$newLocale").log()

            }

            override fun onSystemLocaleChange(oldLocale: Locale, newLocale: Locale) {
                ("监听到系统切换了语种，旧语种：" + oldLocale + "，新语种：" + newLocale + "，是否跟随系统：" + MultiLanguages.isSystemLanguage()).log(
                    "20230804"
                )
            }
        })

    }

    private fun intAutoSize() {
        //开启支持 Fragment 自定义参数的功能
        AutoSizeConfig.getInstance().isCustomFragment = true

        AutoSize.initCompatMultiProcess(this)

        AutoSizeConfig.getInstance().setLog(true)

        //屏幕适配监听器
        AutoSizeConfig.getInstance().onAdaptListener = object : onAdaptListener {
            override fun onAdaptBefore(target: Any, activity: Activity) {
                //使用以下代码, 可以解决横竖屏切换时的屏幕适配问题
                //首先设置最新的屏幕尺寸，ScreenUtils.getScreenSize(activity) 的参数一定要不要传 Application !!!
                AutoSizeConfig.getInstance().screenWidth = ScreenUtils.getScreenSize(activity)[0]
                AutoSizeConfig.getInstance().screenHeight = ScreenUtils.getScreenSize(activity)[1]
                //根据屏幕方向，设置设计尺寸
                if (activity.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    //设置横屏设计尺寸
                    AutoSizeConfig.getInstance().setDesignWidthInDp(812).designHeightInDp = 375
                } else {
                    //设置竖屏设计尺寸
                    AutoSizeConfig.getInstance().setDesignWidthInDp(375).designHeightInDp = 812
                }
            }

            override fun onAdaptAfter(target: Any, activity: Activity) {}
        }
    }


}