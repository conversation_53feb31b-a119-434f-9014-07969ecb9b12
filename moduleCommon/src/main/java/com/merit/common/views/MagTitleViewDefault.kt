package  com.merit.common.views

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import com.merit.common.R
import com.merit.common.utils.getTextTypeface
import com.v.base.utils.vbDrawable
import net.lucode.hackware.magicindicator.buildins.ArgbEvaluatorHolder
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.ColorTransitionPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.SimplePagerTitleView

class MagTitleViewDefault(
    context: Context,
    normalColor: Int,
    selectedColor: Int,
    private val isBold: Boolean
) :
    ColorTransitionPagerTitleView(context) {

    init {
        textSize = 18f
        this.normalColor = normalColor
        this.selectedColor = selectedColor
        this.typeface = context.getTextTypeface(2)
    }

    private var listener: SelectListener? = null

    private var minScale = 0.9f
    private val mChangePercent = 0.6f

    override fun onEnter(
        index: Int,
        totalCount: Int,
        enterPercent: Float,
        leftToRight: Boolean
    ) {
        super.onEnter(index, totalCount, enterPercent, leftToRight)
        if (enterPercent >= mChangePercent) {
            listener?.onEnter(index)
            scaleX = minScale + (1.0f - minScale) * enterPercent
            scaleY = minScale + (1.0f - minScale) * enterPercent
            if (isBold) {
                typeface = Typeface.defaultFromStyle(Typeface.BOLD)
            }
        }

    }

    override fun onLeave(
        index: Int,
        totalCount: Int,
        leavePercent: Float,
        leftToRight: Boolean
    ) {
        super.onLeave(index, totalCount, leavePercent, leftToRight)
        if (leavePercent >= mChangePercent) {
            listener?.onLeave(index)
            scaleX = 1.0f + (minScale - 1.0f) * leavePercent
            scaleY = 1.0f + (minScale - 1.0f) * leavePercent
            if (isBold) {
                typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            }
        }

    }


    fun setSelectListener(listener: SelectListener) {
        this.listener = listener
    }

    interface SelectListener {
        fun onEnter(index: Int)

        fun onLeave(index: Int)
    }

}
