package com.merit.common.views

import android.content.Context
import android.graphics.Color
import android.view.Gravity
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView

class IconTextPagerTitleView(context: Context) : LinearLayout(context), IPagerTitleView {
    private val mTextView: TextView = TextView(context)
    private val mImageView: ImageView = ImageView(context)
    private var mNormalColor: Int = Color.GRAY
    private var mSelectedColor: Int = Color.BLUE

    init {
        orientation = VERTICAL
        gravity = Gravity.CENTER
        addView(mImageView)
        addView(mTextView)
    }

    fun setNormalColor(normalColor: Int) {
        mNormalColor = normalColor
    }

    fun setSelectedColor(selectedColor: Int) {
        mSelectedColor = selectedColor
    }

    fun setText(text: String) {
        mTextView.text = text
    }

    fun setIcon(resId: Int) {
        mImageView.setImageResource(resId)
    }

    override fun onSelected(index: Int, totalCount: Int) {
        mTextView.setTextColor(mSelectedColor)
    }

    override fun onDeselected(index: Int, totalCount: Int) {
        mTextView.setTextColor(mNormalColor)
    }

    override fun onLeave(index: Int, totalCount: Int, leavePercent: Float, leftToRight: Boolean) {
        mTextView.setTextColor(blendColor(mSelectedColor, mNormalColor, leavePercent))
    }

    override fun onEnter(index: Int, totalCount: Int, enterPercent: Float, leftToRight: Boolean) {
        mTextView.setTextColor(blendColor(mNormalColor, mSelectedColor, enterPercent))
    }

    private fun blendColor(fromColor: Int, toColor: Int, ratio: Float): Int {
        val inverseRatio = 1f - ratio
        val r = (Color.red(fromColor) * inverseRatio + Color.red(toColor) * ratio).toInt()
        val g = (Color.green(fromColor) * inverseRatio + Color.green(toColor) * ratio).toInt()
        val b = (Color.blue(fromColor) * inverseRatio + Color.blue(toColor) * ratio).toInt()
        return Color.rgb(r, g, b)
    }
}