package com.merit.common.views;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.Scroller;


import com.merit.common.R;
import com.merit.common.utils.CommonExtKt;
import com.merit.common.utils.DrawUtil;
import com.merit.common.utils.TextUtil;

import java.util.HashMap;
import java.util.Map;


/**
 * Created by Administrator on 2018/4/27.
 * 垂直的 尺子选择
 */

public class FreeVertcalSralRulerView extends View {
    private int mLineWidth;//刻度的线的宽度
    private int mMaxLineLength;//刻度的线长度
    private int selectorLine;//选中的线条
    private int mTextMarginRight;//刻度值离刻度的距离
    private float mTextHeight;
    private TextPaint mTextPaint; // 绘制文本的画笔
    private Paint bitmapPaint; // 绘制文本的画笔
    private Paint mLinePaint;//线的画笔
    private int mWidth;//宽度
    private int mHeight;//高度
    private float mOffset; // 默认尺起始点在屏幕中心, offset是指尺起始点的偏移值
    private int mMaxOffset;
    private VelocityTracker mVelocityTracker;
    private Scroller mScroller;
    private int mMinVelocity;
    private int mLastY, mMove;
    private OnValueChangeListener mListener;
    private int textColor = 0X80222222;
    private String normalText = "";
    private int linColor = 0X80222222;
    private float textSize;
    private float labeltextsize;
    private int mValue;
    private int linemagin;
    private int mTotalLine;
    private Bitmap selectorBitmap;

    public FreeVertcalSralRulerView(Context context) {
        this(context, null);
    }

    public FreeVertcalSralRulerView(Context context, AttributeSet attrs) {
        super(context, attrs);

        init(context, attrs);
    }

    /**
     * 设置用于接收结果的监听器
     *
     * @param listener
     */
    public void setValueChangeListener(OnValueChangeListener listener) {
        mListener = listener;
    }


    private void attresCorrection() {

        if (mLineWidth == -1) {
            mLineWidth = DrawUtil.dip2px(1);
        }
        if (mTextMarginRight == -1) {
            mTextMarginRight = DrawUtil.dip2px(11);
        }
        if (textSize == -1) {
            textSize = DrawUtil.sp2px(16);
        }
    }

    /**
     * 初始化
     *
     * @param context
     */
    private void init(Context context, AttributeSet attrs) {
        //得到自定义属性
        DrawUtil.resetDensity(context.getApplicationContext());
        mScroller = new Scroller(context);
        mMinVelocity = ViewConfiguration.get(getContext()).getScaledMinimumFlingVelocity();
        if (attrs != null) {
            //获取自定义属性
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.VertcalSralRulerView);
            //获取sussic颜色
            mLineWidth = a.getDimensionPixelSize(R.styleable.VertcalSralRulerView_linheight, -1);
            selectorLine = a.getDimensionPixelSize(R.styleable.VertcalSralRulerView_selectorLine, -1);
            mMaxLineLength = a.getDimensionPixelSize(R.styleable.VertcalSralRulerView_maxlinlenght, -1);
            mTextMarginRight = a.getDimensionPixelSize(R.styleable.VertcalSralRulerView_textmarglin, -1);
            linemagin = a.getDimensionPixelSize(R.styleable.VertcalSralRulerView_linemagin, -1);
            textSize = a.getDimensionPixelSize(R.styleable.VertcalSralRulerView_textsize, -1);
            labeltextsize = a.getDimensionPixelSize(R.styleable.VertcalSralRulerView_labeltextsize, -1);
            linColor = a.getColor(R.styleable.VertcalSralRulerView_lincolor, 0X80222222);
            textColor = a.getColor(R.styleable.VertcalSralRulerView_textcolor, 0X80222222);
            normalText = a.getString(R.styleable.VertcalSralRulerView_normalText);
            int bimapId = a.getResourceId(R.styleable.VertcalSralRulerView_selectorImage, 0);
            if (bimapId != 0) {
                selectorBitmap = BitmapFactory.decodeResource(getResources(), bimapId);
            }
            //最后记得将TypedArray对象回收
            a.recycle();
            //进行参数修正
            attresCorrection();
        } else {
            mLineWidth = DrawUtil.dip2px(1);
            mTextMarginRight = DrawUtil.dip2px(11);
            textSize = DrawUtil.sp2px(16);
        }
        mTextPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        Typeface tf = CommonExtKt.getTextTypeface(getContext(), 5);
        mTextPaint.setTypeface(tf);
        mTextPaint.setTextSize(textSize);
        mTextPaint.setColor(textColor);
        mTextHeight = TextUtil.measureHeight(mTextPaint);

        mLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        bitmapPaint = new Paint();
        bitmapPaint.setAntiAlias(true);
        mLinePaint.setStrokeWidth(mLineWidth);
        mLinePaint.setColor(linColor);
    }

    private String[] data = new String[0];
    private HashMap<Integer, String> labelMap = new HashMap<>();

    public void setLabelMap(HashMap<Integer, String> labelMap) {
        this.labelMap = labelMap;
    }

    private String until = "";

    public void setDate(String[] data, int position, String until) {
        this.until = until;
        setDate(data, position);
    }

    public void setDate(String[] data, int position) {
        this.data = data;
        mTotalLine = data.length;
        mValue = position;
        mOffset = -mValue * (mHeight / mTotalLine); // 矫正位置,保证不会停留在两个相邻刻度之间
        postInvalidate();
    }

    public void setNormalText(String text) {
        normalText = text;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (mTotalLine == 0) {
            return;
        }
        if (w > 0 && h > 0) {
            mWidth = w;
            mHeight = h;
        }
        mOffset = -(mHeight / 2);
        mMaxOffset = -(mHeight - mHeight / mTotalLine);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        //测量试图大小
        int hightMode = MeasureSpec.getMode(heightMeasureSpec);
        int hightSize = MeasureSpec.getSize(heightMeasureSpec);
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);
        //根据模式进行大小设置
        switch (hightMode) {
            case MeasureSpec.UNSPECIFIED: //如果没有指定大小，就设置为默认大小
                mHeight = getHeight();
                mWidth = getWidth();
                break;
            case MeasureSpec.AT_MOST: //如果测量模式是最大取值为size
            case MeasureSpec.EXACTLY: //如果是固定的大小，那就不要去改变它
                //我们将大小取最大值,你也可以取其他值
                mHeight = hightSize;
                mWidth = widthSize;
                break;

        }
        //通过计算得到控件实际宽度
        //计算底部文字高度
        mTextPaint.setTextSize(textSize);
        //设置控件大小
        setMeasuredDimension(mWidth, mHeight);
    }

    private RectF rectF;

    @SuppressLint("DrawAllocation")
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mTotalLine == 0) {
            return;
        }
        float textHeight = mHeight / mTotalLine;
        int srcPointY = mHeight / 2; // 默认表尺起始点在屏幕中心
        float top;
        for (int i = 0; i < mTotalLine; i++) {
            mLinePaint.setColor(linColor);
            mLinePaint.setAntiAlias(true);
            mLinePaint.setStrokeWidth(mLineWidth);
            mLinePaint.setStrokeCap(Paint.Cap.ROUND);
            mTextPaint.setColor(mValue == i ? 0xffff2451 : textColor);
            float textWidth = mTextPaint.measureText(data[i]);
            float maxWidth = mTextPaint.measureText(data[data.length - 2]);

            top = srcPointY + mOffset + i * textHeight;//0 160 320 480 640 800 960 1120 1280
            if (srcPointY != Math.abs(mOffset)) {
                top -= textHeight / 2;
            }

            if (labelMap.containsKey(i) && labelMap.get(i) != null || (i == 0 && !"0".equals(data[0]))) {
                String tempLabelText = "";
                if ((i == 0 && !"0".equals(data[0]))) {
                    tempLabelText = normalText;
                } else {
                    tempLabelText = labelMap.get(i);
                }
                mTextPaint.setTextSize(labeltextsize);
                float labelTextWidth = mTextPaint.measureText(tempLabelText);
                canvas.drawText(tempLabelText, mWidth / 2f - labelTextWidth / 2f, top + TextUtil.measureHeight(mTextPaint) * 2 + (textHeight - TextUtil.measureHeight(mTextPaint)) / 2f - mLineWidth, mTextPaint);
                mTextPaint.setTextSize(textSize);
                canvas.drawText(data[i], mWidth / 2f - textWidth / 2f, top + mTextHeight - mLineWidth, mTextPaint);
            } else {
                if (i == 0) {
                    mTextPaint.setTextSize(labeltextsize);
                    float labelTextWidth = mTextPaint.measureText(normalText);
                    canvas.drawText(normalText, mWidth / 2f - labelTextWidth / 2f, top + TextUtil.measureHeight(mTextPaint) + (textHeight - TextUtil.measureHeight(mTextPaint)) / 2f, mTextPaint);
                    mTextPaint.setTextSize(textSize);
                } else {
                    canvas.drawText(data[i], mWidth / 2f - textWidth / 2, top + mTextHeight + (textHeight - mTextHeight) / 2 - mLineWidth, mTextPaint);
                }
            }


            canvas.drawLine(linemagin, top + mLineWidth + (textHeight - mLineWidth) / 2, linemagin + mMaxLineLength, top + mLineWidth + (textHeight - mLineWidth) / 2, mLinePaint);


        }
        if (selectorBitmap != null) {
            rectF = new RectF(linemagin - mLineWidth / 3f, srcPointY - selectorBitmap.getHeight() / 2f, linemagin - mLineWidth / 3f + selectorBitmap.getWidth(), srcPointY + selectorBitmap.getHeight() / 2f);
            canvas.drawBitmap(selectorBitmap, null, rectF, bitmapPaint);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        int yPosition = (int) event.getY();
        if (mVelocityTracker == null) {
            mVelocityTracker = VelocityTracker.obtain();
        }
        mVelocityTracker.addMovement(event);
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                mScroller.forceFinished(true);
                mLastY = yPosition;
                mMove = 0;
                break;
            case MotionEvent.ACTION_MOVE:
                mMove = (mLastY - yPosition);
                changeMoveAndValue();
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                countMoveEnd();
                countVelocityTracker();
                return false;
            // break;
            default:
                break;
        }

        mLastY = yPosition;
        return true;
    }

    private void countVelocityTracker() {//滚动速度
        mVelocityTracker.computeCurrentVelocity(500);
        float yVelocity = mVelocityTracker.getYVelocity();
        if (Math.abs(yVelocity) > mMinVelocity) {
            mScroller.fling(0, 0, 0, (int) yVelocity, Integer.MIN_VALUE, Integer.MAX_VALUE, 0, 0);
        }
    }

    private void countMoveEnd() {
        mOffset -= mMove;
        if (mOffset <= mMaxOffset) {
            mOffset = mMaxOffset;
        } else if (mOffset >= 0) {
            mOffset = 0;
        }
        mLastY = 0;
        mMove = 0;
        mValue = Math.round(Math.abs(mOffset) / (mHeight / mTotalLine));
        mOffset = -mValue * (mHeight / mTotalLine); // 矫正位置,保证不会停留在两个相邻刻度之间
        notifyValueChange();
        postInvalidate();
    }

    private void changeMoveAndValue() {
        mOffset -= mMove;
        if (mOffset <= mMaxOffset) {
            mOffset = mMaxOffset;
            mMove = 0;
            mScroller.forceFinished(true);
        } else if (mOffset >= 0) {
            mOffset = 0;
            mMove = 0;
            mScroller.forceFinished(true);
        }
        mValue = Math.round(Math.abs(mOffset) / (mHeight / mTotalLine));
        notifyValueChange();
        postInvalidate();
    }

    private void notifyValueChange() {
        if (null != mListener) {
            mListener.onValueChange(mValue);
        }
    }

    public interface OnValueChangeListener {
        void onValueChange(int value);
    }

    public void recycle() {
        if (selectorBitmap != null) {
            selectorBitmap.recycle();
            selectorBitmap = null;
        }
        rectF = null;
        mTextPaint = null;
        bitmapPaint = null;
        mLinePaint = null;
    }
}
