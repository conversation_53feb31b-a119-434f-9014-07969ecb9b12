package com.merit.common.views;


import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 主页vp2适配器
 */
public class TabPagerAdapter extends FragmentStateAdapter {
    private List<Fragment> list;
    private Set<Long> createdIds = new HashSet<>();

    public TabPagerAdapter(FragmentManager fragmentManager, Lifecycle lifecycle, List<Fragment> list) {
        super(fragmentManager, lifecycle);
        this.list = list;
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public boolean containsItem(long itemId) {
        return createdIds.contains(itemId);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        createdIds.add((long) position);
        return list.get(position);
    }

}
