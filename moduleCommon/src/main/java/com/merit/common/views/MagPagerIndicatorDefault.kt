package com.merit.common.views

import android.content.Context
import android.graphics.Color
import android.view.animation.AccelerateDecelerateInterpolator
import com.v.base.utils.vbDp2px2Float
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator

class MagPagerIndicatorDefault(context: Context?) : LinePagerIndicator(context) {
    init {
        mode = MODE_WRAP_CONTENT
//        lineWidth = 20.vbDp2px2Float()
//        lineHeight = 3.vbDp2px2Float()
//        roundRadius = 2.vbDp2px2Float()
        setColors(Color.parseColor("#FF2451"))
        startInterpolator = AccelerateDecelerateInterpolator() //设置指示条插值器
    }


}