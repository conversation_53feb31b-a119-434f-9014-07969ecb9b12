package  com.merit.common.views;

import android.content.Context
import android.graphics.Color
import android.graphics.ColorFilter
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.core.graphics.drawable.DrawableCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.viewpager.widget.ViewPager
import com.merit.common.views.MagTitleViewDefault.SelectListener
import com.v.base.VBFragmentAdapter
import net.lucode.hackware.magicindicator.MagicIndicator
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.UIUtil
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator.MODE_MATCH_EDGE

class IndicatorIconText(
    context: Context,
    fragmentManager: FragmentManager,
    val viewPager: ViewPager,
    titles: ArrayList<String>,
    icons: ArrayList<Int>,
    iconSize: Int,
    fragments: ArrayList<Fragment>? = null,
    val normalColor: Int = Color.parseColor("#999999"),//文本未选中颜色
    val selectedColor: Int = Color.parseColor("#1C1A1A"),//文本选中颜色
    val isBold: Boolean = true
) : CommonNavigator(context) {
    init {

        if (fragments != null) {
            val fragmentAdapter = VBFragmentAdapter(
                fragmentManager, fragments, titles.toArray(arrayOfNulls<String>(titles.size))
            )
            viewPager.adapter = fragmentAdapter
        }
        viewPager.offscreenPageLimit = titles.size
        this.adapter = object : CommonNavigatorAdapter() {
            override fun getCount(): Int {
                return if (titles.isEmpty()) 0 else titles.size
            }

            override fun getTitleView(context: Context, index: Int): IPagerTitleView? {
                val simplePagerTitleView = MagTitleViewDefault(context, normalColor, selectedColor, isBold)
                val drawable = resources.getDrawable(icons[index], null)
                simplePagerTitleView.setSelectListener(object : SelectListener {
                    override fun onEnter(index: Int) {
                        DrawableCompat.setTint(drawable, Color.parseColor("#1C1A1A"))
                    }

                    override fun onLeave(index: Int) {
                        DrawableCompat.setTint(drawable, Color.parseColor("#8D8C8C"))
                    }
                })
                simplePagerTitleView.text = "  " + titles[index] + "  "
                drawable.setBounds(0, 0, iconSize, iconSize)
                simplePagerTitleView.setCompoundDrawablesRelative(drawable, null, null, null)
                simplePagerTitleView.setOnClickListener { viewPager.currentItem = index }
                return simplePagerTitleView
            }

            override fun getIndicator(context: Context?): IPagerIndicator? {
                return LinePagerIndicator(context).apply {
                    mode = MODE_MATCH_EDGE
                    lineWidth = UIUtil.dip2px(context, 5.0).toFloat()
                    setColors(Color.parseColor("#FF2451"))
                    startInterpolator = AccelerateDecelerateInterpolator() //设置指示条插值器
                }
            }
        }

    }

    fun create(magicIndicator: MagicIndicator) {
        magicIndicator.navigator = this
        ViewPagerHelper.bind(magicIndicator, viewPager)
    }


}
