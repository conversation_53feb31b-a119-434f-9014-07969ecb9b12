package com.merit.common.views

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.merit.common.R
import com.v.base.utils.vbDp2px2Float

/**
 * author  : ww
 * desc    : 任意View边沿渐变透明
 * time    : 2022/10/13 11:30 上午
 */
class EdgeTransparentView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {
    private val mPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            style = Paint.Style.FILL
            xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)
            shader = LinearGradient(0f,
                0f,
                0f,
                drawSize,
                mGradientColors,
                mGradientPosition,
                Shader.TileMode.CLAMP)
        }

    }
    private var position = 0
    private var drawSize = 0f
    private val topMask = 0x01
    private val bottomMask = topMask shl 1
    private val leftMask = topMask shl 2
    private val rightMask = topMask shl 3
    private var mWidth = 0
    private var mHeight = 0

    //渐变颜色
    private val mGradientColors =
        intArrayOf(Color.parseColor("#ffffffff"), Color.parseColor("#00000000"))

    //渐变位置
    private val mGradientPosition = floatArrayOf(0f, 1f)

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.EdgeTransparentView)
        position = typedArray.getInt(R.styleable.EdgeTransparentView_edge_position, 0)
        drawSize = typedArray.getDimension(R.styleable.EdgeTransparentView_edge_width, 20.vbDp2px2Float())

        typedArray.recycle()
    }

    override fun dispatchDraw(canvas: Canvas) {
        super.dispatchDraw(canvas)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mWidth = width
        mHeight = height
    }


    override fun drawChild(canvas: Canvas, child: View, drawingTime: Long): Boolean {
        val layerSave =
            canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null, Canvas.ALL_SAVE_FLAG)
        val drawChild = super.drawChild(canvas, child, drawingTime)
        if (position == 0 || position and topMask != 0) {
            canvas.drawRect(0f, 0f, mWidth.toFloat(), drawSize, mPaint)
        }
        if (position == 0 || position and bottomMask != 0) {
            val save = canvas.save()
            canvas.rotate(180f, (mWidth / 2).toFloat(), (mHeight / 2).toFloat())
            canvas.drawRect(0f, 0f, mWidth.toFloat(), drawSize, mPaint)
            canvas.restoreToCount(save)
        }
        val offset = (mHeight - mWidth) / 2
        if (position == 0 || position and leftMask != 0) {
            val saveCount = canvas.save()
            canvas.rotate(90f, (mWidth / 2).toFloat(), (mHeight / 2).toFloat())
            canvas.translate(0f, offset.toFloat())
            canvas.drawRect((0 - offset).toFloat(),
                0f,
                (mWidth + offset).toFloat(),
                drawSize,
                mPaint)
            canvas.restoreToCount(saveCount)
        }
        if (position == 0 || position and rightMask != 0) {
            val saveCount = canvas.save()
            canvas.rotate(270f, (mWidth / 2).toFloat(), (mHeight / 2).toFloat())
            canvas.translate(0f, offset.toFloat())
            canvas.drawRect((0 - offset).toFloat(),
                0f,
                (mWidth + offset).toFloat(),
                drawSize,
                mPaint)
            canvas.restoreToCount(saveCount)
        }
        canvas.restoreToCount(layerSave)
        return drawChild
    }


}