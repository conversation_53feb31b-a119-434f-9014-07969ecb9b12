package com.merit.common.views

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.ColorInt
import com.bumptech.glide.Glide
import com.merit.common.R
import com.merit.common.StringKeyConstant
import com.merit.common.utils.StringManagerUtils
import com.scwang.smart.refresh.layout.api.RefreshHeader
import com.scwang.smart.refresh.layout.api.RefreshKernel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import com.scwang.smart.refresh.layout.constant.SpinnerStyle
import com.v.base.utils.vbGetString

class MyClassicsHeader : LinearLayout, RefreshHeader {

    private lateinit var tvTitle: TextView

    constructor(context: Context) : super(context) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initView(context)
    }

    private fun initView(context: Context) {
        gravity = Gravity.CENTER
        LayoutInflater.from(context).inflate(R.layout.app_header, this, true)
        val imageView = findViewById<ImageView>(R.id.ivIcon)
        tvTitle = findViewById(R.id.tvTitle)
        Glide.with(this.context)
            .asGif()
            .load(R.mipmap.base_classics_header)
            .into(imageView)
    }

    override fun getView(): View {
        return this
    }

    override fun getSpinnerStyle(): SpinnerStyle {
        return SpinnerStyle.Translate
    }

    override fun onStartAnimator(layout: RefreshLayout, headHeight: Int, maxDragHeight: Int) {}
    override fun onFinish(layout: RefreshLayout, success: Boolean): Int {
        return 0
    }

    override fun onStateChanged(
        refreshLayout: RefreshLayout,
        oldState: RefreshState,
        newState: RefreshState,
    ) {
        when (newState) {
            RefreshState.PullDownToRefresh -> {
                tvTitle.text =StringManagerUtils.getString(StringKeyConstant.refresh_pull_down_tx,R.string.srl_header_pulling)
            }

            RefreshState.Refreshing,
            RefreshState.RefreshReleased,
            -> {
                tvTitle.text =StringManagerUtils.getString(StringKeyConstant.refresh_loadding_tx,R.string.srl_header_refreshing)

            }

            RefreshState.ReleaseToRefresh -> {
                tvTitle.text =StringManagerUtils.getString(StringKeyConstant.refresh_release_tx,R.string.srl_header_release)
            }

            RefreshState.ReleaseToTwoLevel -> {
                tvTitle.text =""
                    //context.vbGetString(com.scwang.smart.refresh.header.classics.R.string.srl_header_secondary)
            }
            RefreshState.Loading -> {
                tvTitle.text =""
                   // context.vbGetString(com.scwang.smart.refresh.header.classics.R.string.srl_header_loading)
            }
            else -> {

            }

        }
    }

    override fun isSupportHorizontalDrag(): Boolean {
        return false
    }

    override fun onInitialized(kernel: RefreshKernel, height: Int, maxDragHeight: Int) {}

    override fun onMoving(
        isDragging: Boolean,
        percent: Float,
        offset: Int,
        height: Int,
        maxDragHeight: Int,
    ) {
    }

    override fun onReleased(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {}

    override fun onHorizontalDrag(percentX: Float, offsetX: Int, offsetMax: Int) {}

    override fun setPrimaryColors(@ColorInt vararg colors: Int) {}
}