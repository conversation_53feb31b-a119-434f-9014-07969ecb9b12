package  com.merit.common.views;

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import com.merit.common.R
import com.merit.common.utils.getTextTypeface
import com.v.base.VBFragmentAdapter
import net.lucode.hackware.magicindicator.MagicIndicator
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView


/**
 * <AUTHOR> ww
 * desc    :
 * time    : 2021/12/16
 */
interface OnItemClickListener {
    fun onItemClick(position: Int)
}
class IndicatorZoom(
    context: Context,
    val viewPager: ViewPager,
    titles: Array<String>,
    iconOffs: Array<Int>,
    iconOns: Array<Int>,
    fragments: ArrayList<Fragment>? = null,
    onItemClickListener: OnItemClickListener?=null,
) :

    CommonNavigator(context) {

    init {

        viewPager.offscreenPageLimit = titles.size

        if (fragments != null) {

            if (context is AppCompatActivity) {
                val fragmentAdapter =
                    VBFragmentAdapter(context.supportFragmentManager, fragments, titles)
                viewPager.adapter = fragmentAdapter

            } else if (context is Fragment) {

                context.childFragmentManager
                val fragmentAdapter =
                    VBFragmentAdapter(context.childFragmentManager, fragments, titles)
                viewPager.adapter = fragmentAdapter
            }

        }

        isAdjustMode = true
        adapter = object : CommonNavigatorAdapter() {
            override fun getCount(): Int {
                return if (titles.isEmpty()) 0 else titles.size
            }

            override fun getTitleView(context: Context, index: Int): IPagerTitleView? {

                val commonPagerTitleView = CommonPagerTitleView(context)

                val customLayout: View =
                    LayoutInflater.from(context).inflate(R.layout.view_tab, null)
                val titleImg = customLayout.findViewById<View>(R.id.ivIcon) as ImageView
                val titleText = customLayout.findViewById<View>(R.id.ivTitle) as TextView
                titleText.typeface = context.getTextTypeface(2)
//                val titleH = getViewHeight(titleText)

                titleText.text = titles[index]
                titleImg.setImageResource(iconOffs[index])
                commonPagerTitleView.setContentView(customLayout)
                commonPagerTitleView.onPagerTitleChangeListener = object :
                    CommonPagerTitleView.OnPagerTitleChangeListener {
                    override fun onSelected(index: Int, totalCount: Int) {
                        titleText.setTextColor(Color.parseColor("#FF2451"))
                        titleImg.setImageResource(iconOns[index])
                    }

                    override fun onDeselected(index: Int, totalCount: Int) {
                        titleText.setTextColor(Color.parseColor("#BABABA"))
                        titleImg.setImageResource(iconOffs[index])
                    }

                    override fun onLeave(
                        index: Int,
                        totalCount: Int,
                        leavePercent: Float,
                        leftToRight: Boolean
                    ) {

//                        if (index == 0) {
//                            titleImg.scaleX = 1.3f + (1f - 1.3f) * leavePercent
//                            titleImg.scaleY = 1.3f + (1f - 1.3f) * leavePercent
//
//                            titleText.vbSetViewLayoutParams(h = (titleH * leavePercent).toInt())
//                        } else {
                            titleImg.scaleX = 1.1f + (1f - 1.1f) * leavePercent
                            titleImg.scaleY = 1.1f + (1f - 1.1f) * leavePercent
//                        }
                    }

                    override fun onEnter(
                        index: Int,
                        totalCount: Int,
                        enterPercent: Float,
                        leftToRight: Boolean
                    ) {

//                        if (index == 0) {
//
//                            titleImg.scaleX = 1f + (1.3f - 1f) * enterPercent
//                            titleImg.scaleY = 1f + (1.3f - 1f) * enterPercent
//                            titleText.vbSetViewLayoutParams(h = (titleH - (titleH * enterPercent)).toInt())
//                        } else {
                            titleImg.scaleX = 1f + (1.1f - 1f) * enterPercent
                            titleImg.scaleY = 1f + (1.1f - 1f) * enterPercent
//                        }


                    }
                }

                commonPagerTitleView.setOnClickListener {
                    onItemClickListener?.onItemClick(index)
                    viewPager.currentItem = index
                }
                return commonPagerTitleView
            }

            override fun getIndicator(context: Context?): IPagerIndicator? {
                return null
            }
        }

    }


    fun create(magicIndicator: MagicIndicator) {
        magicIndicator.navigator = this
        ViewPagerHelper.bind(magicIndicator, viewPager)
    }

    fun getViewHeight(view: View): Int {
        view.measure(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT)
        return view.measuredHeight
    }
}
