package  com.merit.common.views;

import android.content.Context
import android.graphics.Color
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.viewpager.widget.ViewPager
import com.v.base.VBFragmentAdapter
import net.lucode.hackware.magicindicator.MagicIndicator
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import java.util.*
import kotlin.collections.ArrayList

/**
 * <AUTHOR> ww
 * desc    : tab带线
 * time    : 2021/12/16
 */
class IndicatorLine(
    context: Context,
    fragmentManager: FragmentManager,
    val viewPager: ViewPager,
    titles: ArrayList<String>,
    fragments: ArrayList<Fragment>? = null,
    val normalColor: Int = Color.parseColor("#999999"),//文本未选中颜色
    val selectedColor: Int = Color.parseColor("#1C1A1A"),//文本选中颜色
    val isBold: Boolean = true
) : CommonNavigator(context) {
    init {

        if (fragments != null) {
            val fragmentAdapter =
                VBFragmentAdapter(
                    fragmentManager,
                    fragments,
                    titles.toArray(arrayOfNulls<String>(titles.size))
                )
            viewPager.adapter = fragmentAdapter
        }
        viewPager.offscreenPageLimit = titles.size


//        isAdjustMode = titles.size <= 3

        this.adapter = object : CommonNavigatorAdapter() {
            override fun getCount(): Int {
                return if (titles.isEmpty()) 0 else titles.size
            }

            override fun getTitleView(context: Context, index: Int): IPagerTitleView? {
                val simplePagerTitleView =
                    MagTitleViewDefault(context, normalColor, selectedColor, isBold)
                simplePagerTitleView.text = " " + titles[index] + " "
                simplePagerTitleView.setOnClickListener { viewPager.currentItem = index }
                return simplePagerTitleView
            }

            override fun getIndicator(context: Context?): IPagerIndicator? {
                return MagPagerIndicatorDefault(context)
            }
        }

    }

    fun create(magicIndicator: MagicIndicator) {
        magicIndicator.navigator = this
        ViewPagerHelper.bind(magicIndicator, viewPager)
    }


}
