package com.merit.common.sevice;

import android.app.*
import android.content.Intent
import android.graphics.Color
import android.os.Binder
import android.os.Build
import android.os.IBinder
import com.merit.common.R
import com.merit.common.StringKeyConstant
import com.merit.common.utils.StringManagerUtils

/**
 * 设备连接保活防止运动过程中页面被回收
 */
class DeviceService : Service() {
    override fun onBind(intent: Intent): IBinder {
        return DeviceBinder()
    }

    /**
     * 用于binder 跟activity 交互
     */
    private inner class DeviceBinder : Binder() {
        val service: DeviceService
            get() = this@DeviceService
    }

    override fun onCreate() {
        super.onCreate()
        startForegroundService()
    }

    //Service 启动时调用
    override fun onStartCommand(intent: Intent, flags: Int, startID: Int): Int {
        return START_STICKY
    }

    private fun startForegroundService() {
        val notification = buildNotification()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForeground(NOTIFICATION_ID, notification)
        } else {
            val nm = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            nm.notify(NOTIFICATION_ID, notification)
        }
    }

    private var notificationManager: NotificationManager? = null
    var isCreateChannel = false
    private fun buildNotification(): Notification {
        var builder: Notification.Builder? = null
        var notification: Notification? = null
        if (Build.VERSION.SDK_INT >= 26) {
            //Android O上对Notification进行了修改，如果设置的targetSDKVersion>=26建议使用此种方式创建通知栏
            if (null == notificationManager) {
                notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            }
            val channelId = packageName
            if (!isCreateChannel) {
                val notificationChannel = NotificationChannel(
                    channelId,
                    NOTIFICATION_CHANNEL_NAME, NotificationManager.IMPORTANCE_DEFAULT
                )
                notificationChannel.enableLights(true) //是否在桌面icon右上角展示小圆点
                notificationChannel.lightColor = Color.RED //小圆点颜色
                notificationChannel.setShowBadge(true) //是否在久按桌面图标时显示此渠道的通知
                notificationManager!!.createNotificationChannel(notificationChannel)
                isCreateChannel = true
            }
            builder = Notification.Builder(applicationContext, channelId)
        } else {
            builder = Notification.Builder(applicationContext)
        }
        builder.setSmallIcon(R.mipmap.icon_launcher)
            .setContentTitle(getString(R.string.app_name))
            .setContentText(StringManagerUtils.getString(StringKeyConstant.string_device_foreground_service,R.string.string_device_foreground_service))
            .setContentIntent(
                PendingIntent.getActivity(
                    this,
                    0,
                    this.packageManager.getLaunchIntentForPackage(this.packageName),
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else PendingIntent.FLAG_UPDATE_CURRENT
                )
            )
            .setWhen(System.currentTimeMillis())
        notification = builder.build()
        return notification
    }

    private fun stopForegroundService() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            stopForeground(true)
        } else {
            val nm = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            nm.cancel(NOTIFICATION_ID)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopForegroundService()
    }

    companion object {
        const val NOTIFICATION_ID = 666
        private const val NOTIFICATION_CHANNEL_NAME = "backgroundLocation"
    }
}