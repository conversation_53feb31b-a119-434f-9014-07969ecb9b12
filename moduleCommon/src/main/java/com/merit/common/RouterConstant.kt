package com.merit.common

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import com.didi.drouter.api.DRouter
import com.didi.drouter.api.Extend
import com.didi.drouter.router.RouterCallback.ActivityCallback
import com.merit.common.RouterConstant.RouterLiveVideoResultActivity.Companion.CHALLENGE_TYPE
import com.mrk.device.MrkDeviceManger
import com.mrk.device.bean.DeviceGoConnectBean
import com.tencent.mmkv.MMKV


object RouterConstant {

    const val ROUTER_FRAGMENT_WORKOUTS = "WorkoutsFragment"
    const val ROUTER_FRAGMENT_COURSE = "CourseFragment"
    const val ROUTER_FRAGMENT_ME = "MeFragment"

    /**
     * router 跳转
     */
    fun routerGo(
        context: Context,
        path: String,
        bundle: Bundle? = null,
        activityResult: ((resultCode: Int, intent: Intent?) -> Unit)? = null,
    ) {

        val dRouter = DRouter.build(path)
        if (bundle != null) {
            dRouter.putExtras(bundle)
        }
        if (activityResult == null) {
            dRouter.start(context)
        } else {
            dRouter.start(context, object : ActivityCallback() {
                override fun onActivityResult(resultCode: Int, intent: Intent?) {
                    activityResult.invoke(resultCode, intent)
                }
            })
        }

    }


    /**
     * router onActivityResult
     */
    fun routerFinish(
        activity: Activity,
        bundle: Bundle? = null,
    ) {

        val intent = activity.intent
        bundle?.let {
            intent.putExtras(it)
        }
        activity.setResult(AppCompatActivity.RESULT_OK, intent)
        activity.finish()
    }


    class RouterOnlineSport {
        companion object {
            const val PATH = "OnlineSportListActivity"
            const val SPORT_RACE_ID = "sportRaceId"

        }

        fun go(
            context: Context,
            sportRaceId: String,
            activityResult: ((resultCode: Int, intent: Intent?) -> Unit)? = null,
        ) {
            routerGo(context, PATH, bundleOf(SPORT_RACE_ID to sportRaceId), activityResult)
        }
    }


    /**
     * 登录跳转
     */
    class RouterLogin {
        companion object {
            const val PATH = "LoginActivity"
            const val WHAT = "what"
        }

        fun go(
            context: Context,
        ) {
            commonViewModel.userBean.postValue(null)
            commonViewModel.loginBean.postValue(null)
            MMKV.defaultMMKV().putString(AppConstant.MMKV_LOGIN, "")
            DRouter.build(PATH).putExtra(
                Extend.START_ACTIVITY_FLAGS,
                Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            ).start(context)
            MrkDeviceManger.clear(context)
        }


        fun go(
            context: Context, what: String
        ) {
            commonViewModel.userBean.postValue(null)
            commonViewModel.loginBean.postValue(null)
            MMKV.defaultMMKV().putString(AppConstant.MMKV_LOGIN, "")
            DRouter.build(PATH).putExtra(
                Extend.START_ACTIVITY_FLAGS,
                Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            ).putExtra(WHAT, what).start(context)
        }

    }

    /**
     * 完成用户信息
     */
    class RouterCompleteInfo {
        companion object {
            const val PATH = "CompleteInfoActivity"
        }

        fun go(
            context: Context,
        ) {
            routerGo(context, PATH)

        }
    }


    /**
     * 播放页跳转
     */
    class RouterPlayer {
        companion object {
            const val PATH = "PlayerActivity"
            const val BEAN = "bean"
            const val VIDEO_TYPE = "videoType"
        }

        fun go(
            context: Context,
            bundle: Bundle? = null,
            videoType: Int = 0,//视频类型 1实景视频 0其他视频
            activityResult: ((resultCode: Int, intent: Intent?) -> Unit)? = null,
        ) {
            bundle?.putInt(VIDEO_TYPE, videoType)
            routerGo(context, PATH, bundle, activityResult)
        }
    }

    /**
     * H5页跳转
     */
    class RouterWebView {
        companion object {
            const val PATH = "WebViewActivity"
            const val BEAN = "bean"
        }

        fun go(
            context: Context,
            bundle: Bundle? = null,
            activityResult: ((resultCode: Int, intent: Intent?) -> Unit)? = null,
        ) {
            routerGo(context, PATH, bundle, activityResult)
        }
    }

    /**
     * 设备列表的跳转
     */
    class RouterDeviceList {
        companion object {
            const val PATH = "DeviceListActivity"
            const val PRODUCT_ID = "productId"//设备类型id
            const val DEVICE_NAME = "deviceName"//设备名称(如果这个值有 就表示只连接这个设备 所以过滤其他的)
            const val IS_CONNECT_BACK = "IS_CONNECT_BACK"//连接成功是否需要返回(并且不能进入设备详情)
        }

        /**
         * @param productId 设备大类id
         * @param isConnectBack 连接成功是否需要返回
         * @param deviceName 设备名称 如果有设备名称则表示只显示该设备
         */
        fun go(
            context: Context,
            productId: String,
            isConnectBack: Boolean = false,
            deviceName: String = "",
            activityResult: ((resultCode: Int, intent: Intent?) -> Unit)? = null
        ) {
            routerGo(
                context, PATH, bundleOf(
                    PRODUCT_ID to productId,
                    DEVICE_NAME to deviceName,
                    IS_CONNECT_BACK to isConnectBack
                ), activityResult
            )
        }

    }

    /**
     * 设备详情
     */
    class RouterDeviceInfoActivity {
        companion object {
            const val PATH = "DeviceInfoActivity"
            const val IS_CONNECT_BACK = "IS_CONNECT_BACK"//连接成功是否需要返回
            const val BEAN = "bean"//连接成功是否需要返回(并且不能进入设备详情)
        }

        fun go(
            context: Context,
            bean: DeviceGoConnectBean,
            isConnectBack: Boolean = false,
            activityResult: ((resultCode: Int, intent: Intent?) -> Unit)? = null
        ) {
            routerGo(
                context, PATH, bundleOf(
                    BEAN to bean,
                    IS_CONNECT_BACK to isConnectBack
                ), activityResult
            )
        }

    }

    /**
     * MainActivity
     * 切换语言时可以调用
     */
    class RouterMainActivity {
        companion object {
            const val PATH = "MainActivity"
            const val IS_TO_SEARCH_EQUIPMENT = "linkToSearchEquipment"
            const val PRODUCT_ID = "productId"//设备类型id

        }

        fun go(context: Context,isToSearchEquipment: Boolean = false,  productId: String="") {
            DRouter.build(PATH).putExtra(
                Extend.START_ACTIVITY_FLAGS,
                Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            ).putExtras(bundleOf(IS_TO_SEARCH_EQUIPMENT to isToSearchEquipment,PRODUCT_ID to productId)).start(context)
        }

    }


    class RouterLiveVideoResultActivity {
        companion object {
            const val PATH = "LiveVideoResultActivity"
            const val CHALLENGE_TYPE = "challenge_type"
        }

        fun go(context: Context, from: Int? = 0) {
            routerGo(context, PATH, bundleOf(CHALLENGE_TYPE to from))
        }
    }

    /**
     *
     * 累计训练
     */
    class RouterExerciseDataActivity {
        companion object {
            const val PATH = "ExerciseDataActivity"
        }

        fun go(context: Context) {
            routerGo(context, PATH)
        }

    }

    /**
     *
     * VIP
     */
    class RouterVIPActivity {
        companion object {
            const val PATH = "VipActivity"
        }

        fun go(context: Context) {
            routerGo(context, PATH)
        }

    }


    /**
     *
     * 个人信息
     */
    class RouterMyDataActivity {
        companion object {
            const val PATH = "MyDataActivity"
        }

        fun go(context: Context) {
            routerGo(context, PATH)
        }

    }


    /**
     * 课程详情(超燃脂自由训练,也就是音乐训练)
     */
    class RouterUltraBurningDetailsActivity {
        companion object {
            const val PATH = "UltraBurningDetailsActivity"
            const val ID = "id"
            const val COVER = "cover"
            const val TITLE = "title"
            const val IS_GIF = "isGif"
        }

        fun go(
            context: Context,
            bundle: Bundle,
            id: String,
            cover: String,
            title: String,
            activityResult: ((resultCode: Int, intent: Intent?) -> Unit)? = null,
        ) {

            val isGif = cover.endsWith(".gif")

            DRouter.build(PATH).apply {
                //元素共享使用gif图 gif会暂停所有这里如果不是gif才使用元素共享
                if (!isGif) {
                    putExtra(
                        Extend.START_ACTIVITY_OPTIONS, bundle
                    )
                }
            }.putExtra(ID, id).putExtra(COVER, cover).putExtra(TITLE, title).putExtra(IS_GIF, isGif)
                .start(context, object : ActivityCallback() {
                    override fun onActivityResult(resultCode: Int, intent: Intent?) {
                        activityResult?.invoke(resultCode, intent)
                    }
                })
        }

    }


    /**
     *
     * 设备搜索
     */
    class RouterDeviceSearchActivity {
        companion object {
            const val PATH = "DeviceSearchActivity"
            const val PRODUCT_ID = "productId"//设备类型id
        }

        fun go(
            context: Context,
            productId: String,
            activityResult: ((resultCode: Int, intent: Intent?) -> Unit)? = null
        ) {
            routerGo(
                context, PATH, bundleOf(
                    PRODUCT_ID to productId
                ), activityResult
            )
        }

    }

    /**
     *
     * 我绑定的设备列表
     */
    class RouterMyBindDeviceListActivity {
        companion object {
            const val PATH = "MyBindDeviceListActivity"
        }

        fun go(
            context: Context,
        ) {
            routerGo(
                context, PATH
            )
        }

    }
}