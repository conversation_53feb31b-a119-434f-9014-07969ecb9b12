package com.merit.common

import com.merit.common.utils.EnvUtil
import com.merit.common.utils.NetServerUtil
import com.sea.flutter_middleware.config.FlutterBuildType
import com.v.log.util.log


object AppConstant {

    const val EVENT_MAIN_TAB = "EVENT_MAIN_TAB"//首页切换tab
    var EVENT_MAIN_TAB_RECOMMEND_ID = ""//首页切换tab 有时候需要跳转到相对应的设备分类

    const val MMKV_SPASH = "MMKV_SPASH"
    const val MMKV_SIGIN = "SIGIN"
    const val MMKV_REGSTER = "REGSTER"

    const val MMKV_LOGIN = "MMKV_LOGIN"//用户信息
    const val MMKV_LOCATION = "MMKV_LOCATION"//设备登录的定位信息
    const val MMKV_FIRST_APP = "MMKV_FIRST_APP"//是否第一次打开app
    const val MMKV_UNIT = "MMKV_UNIT"//测量单位
    const val MMKV_NET_SERVER = "MMKV_NET_SERVER"//服务器

    const val MMKV_PLAY_MUSIC_GUIDE = "MMKV_PLAY_MUSIC_GUIDE"//播放页面超燃脂课程新手引导

    const val MMKV_TIP_DATACENTER_GUIDE = "MMKV_TIP_DATACENTER_GUIDE"

    const val MMKV_CURRENT_ADVERT = "MMKV_CURRENT_ADVERT"
    const val MMKV_LOCAL_ADVERT_LIST = "MMKV_LOCAL_ADVERT_LIST"
    const val MAIN_TAB_CLICK = "MAIN_TAB_CLICK"


    fun getBaseWebUrl(): String {
        val status = NetServerUtil.getNetServerStatus()
        return if (!BuildConfig.IS_DEBUG) {
            "https://console-intl.merach.com/webview/1.0.0/"
        } else {
            EnvUtil.getEnv(status)?.urls?.h5 ?: "http://test-console-intl.merach.com/webview/1.0.0/"
        }
    }

    fun getFlutterBuildType(): Int {
        return if (BuildConfig.IS_DEBUG) {
            FlutterBuildType.DEBUG
        } else {
            FlutterBuildType.PROD
        }
    }

    fun getBaseUrl(): String {
        //0日服 1美服 -1美服预发 -2日服预发 -3TestApi  -4UatApi
        val status = NetServerUtil.getNetServerStatus()
        //如果当前服务器等于正式包 则判断服务器是否还为测试
        return if (!BuildConfig.IS_DEBUG) {
            //如果正式是正式包 服务器不等于美服并且不等于日服
            when (status) {
                //正式日服
                0 -> "https://api-jp.merach.com"
                //正式美服
                1 -> "https://api-usa.merach.com"
                //正式欧服
                1000 -> "https://api-eu.merach.com"

                else -> "https://api-usa.merach.com"
            }
        } else {
            EnvUtil.getEnv(status)?.urls?.backend ?: "https://api-usa.merach.com"
        }
    }

    //通过正则提取域名
    fun getWebServer(): String {
        return getBaseUrl()
    }


    val URL_AGREEMENT = getBaseWebUrl() + "user-agreement/index.html"//用户协议
    val URL_NEW_GUIDE = getBaseWebUrl() + "novice-guide/index.html" //新手指引
    val URL_CLAUSE = getBaseWebUrl() + "course-notes/index.html" //Merit注意事项
    val URL_PRIVACY_POLICY = getBaseWebUrl() + "privacy-clause/index.html" //隐私
    val URL_TRAIN_REPORT = getBaseWebUrl() + "training-report/index.html" //训练报告
    val URL_COURSE_DETAIL = getBaseWebUrl() + "course-detail/index.html" //课程详情H5页面
    val URL_DEVICE_EXPLAIN = getBaseWebUrl() + "device-connection/index.html" //设备说明
    val URL_LIVE_VIDEO_DESC = getBaseWebUrl() + "training-report-rules/index.html"//榜单描述
    val URL_LIVE_RACE_DESC = getBaseWebUrl() + "real-time-rules/index.html"//榜单描述
    val HEALTH_QA = getBaseWebUrl() + "qa/index.html"//用户协议
    val DEVICE_ACTIVE = getBaseWebUrl() + "connect-link/index.html"//设备激活页面
    val BADGES_LIST = getBaseWebUrl() + "medal-list/index.html"//勋章墙

}

