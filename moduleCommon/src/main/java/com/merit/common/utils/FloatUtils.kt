package com.merit.common.utils

import com.v.log.util.log
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat
import java.util.Locale
import kotlin.math.floor
import kotlin.math.pow

object FloatUtils {
    fun getFloat(data: Double, index: Int = 2): Double {
        val decimal = BigDecimal(data)
        return decimal.setScale(index, RoundingMode.DOWN).toDouble()
    }

    fun getFloat2(data: Float): String? { //强制保留两位
        val df = DecimalFormat.getInstance(Locale.CHINA) as DecimalFormat
        df.applyPattern("#####0.00")
        return df.format(data)
    }

    fun floatToString(data: Double, unit: Double,index:Int?=-1): String {
        if (data == 0.0)
            return "0"
        val df = DecimalFormat.getInstance(Locale.CHINA) as DecimalFormat
        when(index){
            1->df.applyPattern("#####0.0")
            else-> df.applyPattern("#####0.00")
        }
//        " floatToString $data ${float(data, unit)}".log("floatToString")
        return df.format(float(data, unit))
    }


    private fun float(data: Double, b: Double): Float {
        val decimal = BigDecimal(data)
        val decimal1 = BigDecimal(b)
        return decimal.divide(decimal1, 2, RoundingMode.DOWN).toFloat()
    }

    fun floatTo(data: Double, unit: Double): Float {
        if (data == 0.0)
            return 0f
        return float(data, unit)
    }

    fun unitProcess(
        input: Any?,
        divide: Int = 0, //数据除以1000 60
        returnAsNum: Boolean = false, //是否返回num类型
        roundDownToInteger: Boolean = false, //是否直接返回整数
        fractionDigits: Int = 1
    ): Any {
        val numValue: Double? = when (input) {
            is Int -> input.toDouble()
            is Double -> input
            is String -> input.toDoubleOrNull()
            else -> null
        }

        numValue ?: return if (returnAsNum) 0 else ""

        // 如果需要除以1000
        val finalValue = if (divide != 0) {
            numValue / divide
        } else {
            numValue
        }

        // 根据是否直接取整决定处理方式
        val result = if (roundDownToInteger) {
            floor(finalValue)
        } else {
            (finalValue * 10.0.pow(fractionDigits)).toLong() / 10.0.pow(fractionDigits)
        }

        return if (returnAsNum) {
            result
        } else {
            if (roundDownToInteger) {
                result.toInt().toString()
            } else {
                result.toString()
            }
        }
    }

}