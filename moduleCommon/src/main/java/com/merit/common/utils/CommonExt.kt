package com.merit.common.utils

import android.app.ActivityManager
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.AssetManager
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.Typeface
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.UnderlineSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import com.hjq.language.MultiLanguages
import com.merit.common.MyApplication
import com.merit.common.R
import com.v.base.utils.vbGetLayoutView
import java.math.BigDecimal
import java.math.RoundingMode
import java.security.MessageDigest
import java.util.Formatter
import java.util.Locale
import java.util.regex.Pattern


/**
 * 是否是平板
 */
fun isTablet(): Boolean {
    return ((MyApplication.instance.resources.configuration.screenLayout and Configuration.SCREENLAYOUT_SIZE_MASK) >= Configuration.SCREENLAYOUT_SIZE_LARGE)
}

/**
 * md5 32位加密
 */
fun String.md5Encrypt32(): String {
    var encryptStr = this
    val md5: MessageDigest
    try {
        md5 = MessageDigest.getInstance("MD5")
        val md5Bytes: ByteArray = md5.digest(encryptStr.toByteArray())
        val hexValue = StringBuffer()
        for (i in md5Bytes.indices) {
            val `val` = md5Bytes[i].toInt() and 0xff
            if (`val` < 16) hexValue.append("0")
            hexValue.append(Integer.toHexString(`val`))
        }
        encryptStr = hexValue.toString()
    } catch (e: Exception) {
        throw RuntimeException(e)
    }
    return encryptStr
}

/**
 * md5 16位加密
 */
fun String.md5Encrypt16(): String {
    return this.md5Encrypt32().substring(8, 24)
}


/**
 * 列表空布局
 * @param context
 * @res 图片
 * @msg 提示文字
 * @buttonText 按钮文字 此处有数据则表示需要显示按钮
 * @listener 按钮文字 有值则是按钮的点击回调 按钮文字没值则是空布局的点击回调
 */
fun emptyView(
    context: Context,
    res: Int,
    msg: String,
    buttonText: String = "",
    listener: View.OnClickListener? = null,
): View {

    val view: View = context.vbGetLayoutView(R.layout.view_list_empty)
    val ivIcon = view.findViewById<ImageView>(R.id.ivIcon)
    val tvMsg = view.findViewById<TextView>(R.id.tvMsg)
    val tvSubmit = view.findViewById<TextView>(R.id.tvSubmit)

    ivIcon.setImageResource(res)
    tvMsg.text = msg

    if (buttonText.isNullOrEmpty()) {
        tvSubmit.visibility = View.GONE
        listener?.run {
            view.setOnClickListener(this)
        }
    } else {
        tvSubmit.text = buttonText
        tvSubmit.visibility = View.VISIBLE
        listener?.run {
            tvSubmit.setOnClickListener(this)
        }
    }
    return view
}


/**
 * 保留小数
 * @length 保留位数
 */
fun Double.keepDecimals(length: Int = 2): String {
    val bigDecimal = BigDecimal(this)
    return bigDecimal.setScale(length, BigDecimal.ROUND_HALF_UP).toString()
}


/**
 * 保留小数
 * @length 保留位数
 */
fun String.keepDecimals(length: Int = 2): String {
    return if (this.isNullOrEmpty()) {
        "0.00"
    } else {
        val bigDecimal = BigDecimal(this)
        bigDecimal.setScale(length, BigDecimal.ROUND_HALF_UP).toString()
    }
}

/**
 * 协议颜色处理以及点击
 * 《》中间的文字会变色
 */
//fun TextView.symbolDispose(content: String, onClick: () -> Unit) {
//    val spannable = SpannableStringBuilder(content)
//    val startIndex = content.indexOf(this.context.getString(R.string.string_book_title_left))
//    val endIndex = content.lastIndexOf(this.context.getString(R.string.string_book_title_right)) + 1
//
//    spannable.setSpan(object : ClickableSpan() {
//        override fun onClick(p0: View) {
//            onClick.invoke()
//        }
//
//        override fun updateDrawState(ds: TextPaint) {
//            super.updateDrawState(ds)
//            ds.color = Color.parseColor("#FF2451")
//            ds.isUnderlineText = false
//        }
//
//    }, startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
//
//    this.movementMethod = LinkMovementMethod.getInstance()
//    this.text = spannable
//    this.highlightColor = Color.TRANSPARENT
//}


/**
 * 关键字颜色处理,以及点击
 */
fun TextView.keywordDispose(
    vararg keys: String,
    color: String = "#FF2451",
    onClick: ((position: Int) -> Unit)? = null,
) {

    try {
        if (!this.text.isNullOrEmpty()) {
            val s = SpannableString(this.text)
            for (i in keys.indices) {
                val keyword = keys[i]
                val p = Pattern.compile(keyword)
                val m = p.matcher(s)
                while (m.find()) {
                    val start = m.start()
                    val end = m.end()
                    if (onClick != null) {
                        s.setSpan(object : ClickableSpan() {
                            override fun onClick(widget: View) {
                                onClick.invoke(i)
                            }

                            override fun updateDrawState(ds: TextPaint) {
                                ds.color = Color.parseColor(color)
                                ds.isUnderlineText = false
                            }
                        }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

                    }
                    s.setSpan(UnderlineSpan(), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
            }
            text = s
            movementMethod = LinkMovementMethod.getInstance()
            //点击变色
            this.highlightColor = Color.TRANSPARENT
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }

}


/**
 * 邮箱格式化+*号
 */
fun String.emailFormat(): String {
    try {
        val emailStr = this.substring(0, this.lastIndexOf("@"))
        val substitutionLength = 4//隐藏的位数
        val f = emailStr.length - substitutionLength//总长度减去需要隐藏的位数

        return if (f > 0) {
            val sb = StringBuilder(this)
            sb.replace(f, emailStr.length, "****")
            sb.toString()
        } else {
            val sb = StringBuilder()
            sb.append(this.substring(0, 1))
            for (i in 1 until emailStr.length) {
                sb.append("*")
            }
            sb.append(this.substring(emailStr.length, this.length))
            sb.toString()
        }
    } catch (e: Exception) {
        e.printStackTrace()
        return this
    }

}

/**
 * 校验邮箱
 * @return 校验通过返回true，否则返回false
 */
fun String.isEmail(): Boolean {
    val REGEX_EMAIL =
        "[\\w!#\$%&'*+/=?^_`{|}~-]+(?:\\.[\\w!#\$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+[\\w](?:[\\w-]*[\\w])?"
    return Pattern.matches(REGEX_EMAIL, this)
}


/**
 * 判断密码是否不包含空格
 */
fun String.isPassword(): Boolean {
    return !this.contains("\\s".toRegex())
}

/**
 * //关闭软键盘
 *
 */
fun View.hideInput() {
    val inputMethodManager =
        context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    inputMethodManager.hideSoftInputFromWindow(windowToken, 0)
    clearFocus()
}

/**
 * 开启软键盘
 *
 */
fun View.showInput() {
    isFocusable = true
    isFocusableInTouchMode = true
    requestFocus()
    postDelayed({
        val inputManager =
            context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputManager.showSoftInput(this, 0)
    }, 300)
}


/**
 * 将毫秒秒数转换为hh:mm的格式
 */
fun Long.formattedTime(isHours: Boolean = false): String {

    if (isHours) {
        if (this <= 0 || this >= 24 * 60 * 60 * 1000) {
            return "00:00:00"
        }
    } else {
        if (this <= 0 || this >= 24 * 60 * 60 * 1000) {
            return "00:00"
        }
    }


    val totalSeconds: Long = this / 1000
    val seconds = (totalSeconds % 60).toInt()
    val minutes = (totalSeconds / 60 % 60).toInt()
    val hours = (totalSeconds / 3600).toInt()
    val stringBuilder = StringBuilder()
    val mFormatter = Formatter(stringBuilder, Locale.getDefault())
    return if (hours > 0 || isHours) {
        mFormatter.format("%02d:%02d:%02d", hours, minutes, seconds).toString()
    } else {
        mFormatter.format("%02d:%02d", minutes, seconds).toString()
    }
}

/**
 * 将分钟数转换为hh:mm:ss的格式
 */
fun Int.minuteFormattedTime(): String {

    if (this <= 0) {
        return "00:00"
    }
    val stringBuilder = StringBuilder()
    val mFormatter = Formatter(stringBuilder, Locale.getDefault())
    return if (this > 60) {
        val h = this / 60 % 60
        val m = this / 60 % 60
        mFormatter.format("%02d:%02d:%02d", h, m, 0).toString()
    } else {

        mFormatter.format("%02d:%02d", this, 0).toString()
    }

}

fun Int.minuteFormattedTime2(isHours: Boolean = false): String {
    val hours = this / 60
    val minutesRemaining = this % 60
    if (isHours) {
        return String.format("%02d:%02d", hours, minutesRemaining)
    } else {
        return String.format("%02d:%02d:%02d", hours, minutesRemaining, 0)
    }

}

fun Float.formatOne(): String {
    return if (this % 1 == 0f) {
        this.toInt().toString()
    } else {
        String.format("%.1f", this)
    }
}

fun Float.numDown(): String {
    return kotlin.math.floor(this).toInt().toString()
}

fun Float.numUp(): String {
    return kotlin.math.ceil(this).toInt().toString()
}

fun Float.numRound(): String {
    return kotlin.math.round(this).toInt().toString()
}

fun Float.formatTwo(): String {
    return if (this % 1 == 0f) {
        this.toInt().toString()
    } else {
        String.format("%.2f", this)
    }
}

enum class RoundingType {
    DOWN, UP, ROUND
}

fun Double.formatRound(roundingType: RoundingType, decimalPlaces: Int = 2): String {
    return this.toFloat().formatRound(roundingType, decimalPlaces)
}

/**
 * DOWN 小数点后 decimalPlaces 位向下取整
 * UP 小数点后 decimalPlaces 位向上取整
 * ROUND 小数点后 decimalPlaces 位四舍五入
 */
fun Float.formatRound(roundingType: RoundingType, decimalPlaces: Int = 2): String {
    // 直接创建一个BigDecimal，无需预先设置decimalPlaces + 1
    val stringValue = this.toString()
    val numericValue = BigDecimal(stringValue)

    // 根据roundingType应用所需的取整方法
    val roundedValue = when (roundingType) {
        RoundingType.DOWN -> numericValue.setScale(decimalPlaces, RoundingMode.DOWN)
        RoundingType.UP -> numericValue.setScale(decimalPlaces, RoundingMode.UP)
        RoundingType.ROUND -> numericValue.setScale(decimalPlaces, RoundingMode.HALF_UP)
    }
    // 返回格式化的字符串，保留指定的小数位数
    return roundedValue.toPlainString()
}

//保留一位小数
fun Double.formatOne(): Double {
    return this.toBigDecimal().setScale(1, RoundingMode.HALF_UP).toDouble()
}


fun Double.formatThree(): Double {
    return this.toBigDecimal().setScale(3, RoundingMode.HALF_UP).toDouble()
}

fun Double.formatUp(index: Int): Double {
    return this.toBigDecimal().setScale(index, RoundingMode.HALF_UP).toDouble()
}


fun Double.formatDown(index: Int): Double {
    return this.toBigDecimal().setScale(index, RoundingMode.HALF_DOWN).toDouble()
}

fun Double.formatFour(): Double {
    return this.toBigDecimal().setScale(4, RoundingMode.HALF_UP).toDouble()
}


//多颜色配置
fun getBaseColor(position: Int): String {

    val colorLines = arrayOf(
        "#FF2451",
        "#E314D7",
        "#8B14E3",
        "#3D50E6",
        "#03B8FF",
        "#23D4AA",
        "#00B31E",
        "#FFBE00",
        "#FF9948",
        "#FF764B"
    )
    return colorLines[position % 10]
}


enum class ToastStatus {
    ERROR, WARN, SUCCESS, NONE
}

/**
 * 自定义样式toast,带图片的
 */
fun Context.toastStatus(content: String?, enum: ToastStatus = ToastStatus.NONE) {

    if (content.isNullOrEmpty()) {
        return
    }

    val inflater = this.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    //自定义布局
    val view: View = inflater.inflate(R.layout.base_toast, null)
    val ivCover = view.findViewById<ImageView>(R.id.ivCover)
    val tvContent = view.findViewById<TextView>(R.id.tvContent)

    when (enum) {
        ToastStatus.ERROR -> {
            ivCover.setImageResource(R.mipmap.base_icon_error)
        }

        ToastStatus.WARN -> {
            ivCover.setImageResource(R.mipmap.base_icon_warn)
        }

        ToastStatus.SUCCESS -> {
            ivCover.setImageResource(R.mipmap.base_icon_success)
        }

        else -> {
            ivCover.visibility = View.GONE
        }
    }

    tvContent.text = content
    val toast = Toast(this)
    toast.setGravity(Gravity.CENTER, 0, 0)
    toast.duration = Toast.LENGTH_LONG
    toast.view = view
    toast.show()
}

/**
 * 获取渠道名称
 * @return 返回渠道名称
 */
fun getChannel(): String {
    var channelName = ""
    try {
        val appInfo = MyApplication.instance.applicationContext.packageManager.getApplicationInfo(
            MyApplication.instance.applicationContext.packageName, PackageManager.GET_META_DATA
        )
        channelName = appInfo.metaData.getString("CHANNEL").toString()
    } catch (e: java.lang.Exception) {
        e.printStackTrace()
    }
    return channelName
}

/**
 * 网络请求头需要的参数语言
 */
fun getNetLanguage(): String {
    // 获取当前的语种
    val sava = MultiLanguages.getAppLanguage()
    // 对比两个语言是否是同一个语种（比如：中文的简体和繁体，英语的美式和英式）
    return if (MultiLanguages.equalsLanguage(sava, Locale.CHINA) || MultiLanguages.equalsLanguage(
            sava,
            Locale("zh-hans")
        )
    ) {
        "zh-Hans"
    } else if (MultiLanguages.equalsLanguage(sava, Locale("zh-hant"))) {
        "zh-Hant"
    } else if (MultiLanguages.equalsLanguage(sava, Locale.JAPAN)) {
        "ja"
    } else if (MultiLanguages.equalsLanguage(sava, Locale.FRANCE)) {
        "fr"
    } else if (MultiLanguages.equalsLanguage(sava, Locale.ITALY)) {
        "it"
    } else if (MultiLanguages.equalsLanguage(sava, Locale.GERMAN)) {
        "de"
    } else if (MultiLanguages.equalsLanguage(sava, Locale("es"))) {
        "es"
    } else if (MultiLanguages.equalsLanguage(sava, Locale("ru"))) {
        "ru"
    } else if (MultiLanguages.equalsLanguage(sava, Locale("uk"))) {
        "uk"
    } else {
        "en"
    }
}

//因为后台和手机本地的不一致，因此需要进行转换
fun getNetLanguage(key: String): Locale {
    return if ("zh-Hans" == key) {
        Locale.CHINA
    } else if ("ja" == key) {
        Locale.JAPAN
    } else if ("fr" == key) {
        Locale.FRANCE
    } else if ("it" == key) {
        Locale.ITALY
    } else if ("de" == key) {
        Locale.GERMAN
    } else {
        Locale("en")
    }
}


fun Context.getTextTypeface(fontType: Int): Typeface {
    val mgr: AssetManager = this.assets
    val typefaceAts = when (fontType) {
        1 -> "Montserrat-BoldItalic.ttf"  //粗斜体
        2 -> "Montserrat-Regular.ttf" //正常
        3 -> "Montserrat-Medium.ttf" //正常加粗
        4 -> "Montserrat-SemiBold.ttf" //正常加粗粗
        5 -> "Montserrat-Bold.ttf" //正常加粗粗粗
        else -> "Montserrat-Regular.ttf" //正常
    }
    return Typeface.createFromAsset(mgr, "fonts/${typefaceAts}")

}

/**
 * 判断某个act是否纯在
 */
fun String.isActivityRunning(context: Context): Boolean {
    val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    val runningTasks = activityManager.getRunningTasks(Int.MAX_VALUE)

    for (task in runningTasks) {
        // 遍历任务堆栈中的所有 Activity
        for (stackInfo in task.baseActivity?.let { listOf(it) } ?: emptyList()) {
            if (stackInfo.className == this) {
                return true
            }
        }
    }
    return false
}