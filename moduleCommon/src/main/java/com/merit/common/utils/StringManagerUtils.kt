package com.merit.common.utils

import android.annotation.SuppressLint
import android.content.Context
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.merit.common.bean.LanguageInfo
import com.tencent.mmkv.MMKV
import com.v.log.util.log


@SuppressLint("StaticFieldLeak")
object StringManagerUtils {

    lateinit var mContext: Context

    fun init(context: Context) {
        mContext = context
    }

    @JvmStatic
    var hashMap = HashMap<String, String>()

    fun setStrData(data: ArrayList<LanguageInfo>) {
        hashMap.clear()
        for (item in data) {
            hashMap[item.code] = item.message
        }
        MMKV.defaultMMKV().removeValueForKey("language")
        MMKV.defaultMMKV().close()
        val encodedMap = ObjectMapper().writeValueAsBytes(hashMap)
        MMKV.defaultMMKV().encode("language", encodedMap)
    }

    fun getValueData(): HashMap<String, String> {
        val typeReference: TypeReference<HashMap<String, String>> = object : TypeReference<HashMap<String, String>>() {}
        var languageBytes = MMKV.defaultMMKV().decodeBytes("language")
        if (languageBytes == null || languageBytes.isEmpty()) {
            return HashMap()
        }
        try {
            val storedMap = ObjectMapper().readValue(languageBytes, typeReference)
            return storedMap ?: HashMap()
        } catch (e: Exception) {
            return HashMap()
        }

    }


    fun getString(key: String, id: Int): String {
        return getValueData()[key] ?: mContext.getString(id)
    }


    fun getString(key: String, str: String): String {
        return getValueData()[key] ?: str
    }

}