package com.merit.common.utils

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.os.bundleOf
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.logEvent
import com.merit.common.AppConstant
import com.merit.common.AppConstant.getWebServer
import com.merit.common.BuildConfig
import com.merit.common.MyApplication
import com.merit.common.MyApplication.Companion.firebaseAnalytics
import com.merit.common.RouterConstant
import com.merit.common.bean.WebBean
import com.sea.flutter_middleware.FlutterMiddleware
import com.sea.flutter_middleware.config.FlutterNativePageNames
import com.sea.flutter_middleware.config.option
import com.sea.flutter_middleware.ui.CalenderActivity
import com.sea.flutter_middleware.ui.FeedActivity
import com.sea.flutter_middleware.ui.VipRechargeActivity
import com.tencent.mmkv.MMKV
import com.v.base.utils.vbGetAppVersionCode
import com.v.base.utils.vbGetAppVersionName

object FlutterUtils {

    fun init(applicationContext: Context) {
        FlutterMiddleware.run {
            init(applicationContext)
            setDefaultOptions()
        }
        FlutterMiddleware.cleanListeners()
        FlutterMiddleware.addListener {
            onTrackClick { name, data ->
                firebaseAnalytics.logEvent(name) {
                    data.forEach { (key, value) ->
                        param(key, value)
                    }
                }
            }
            onTrackExpose { name, data ->
                firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
                    param(FirebaseAnalytics.Param.SCREEN_NAME, name)
                    data.forEach { (key, value) ->
                        param(key, value)
                    }
                }
            }
            onOpenNativePage { name, data ->
                when (name) {
                    FlutterNativePageNames.SPORTDATA -> {
                        RouterConstant.RouterExerciseDataActivity().go(applicationContext)
                    }

                    FlutterNativePageNames.ADDEQUIMENT -> {
                        RouterConstant.RouterMyBindDeviceListActivity().go(applicationContext)
                    }

                    FlutterNativePageNames.CALENDER -> {
                        val fromTodayTarget = data?.get("fromTodayTarget") as Boolean? ?: false
                        FlutterMiddleware.startCalenderActivity(applicationContext, fromTodayTarget = fromTodayTarget)
                    }

                    FlutterNativePageNames.PURCHASE_DEVICE -> {
                        val link = data?.get("link") as String?
                        if (link != null) {
                            val bundle = Bundle()
                            bundle.putParcelable(
                                RouterConstant.RouterWebView.BEAN, WebBean(url = link, isShowTitle = true)
                            )
                            RouterConstant.RouterWebView().go(applicationContext, bundle)
                        }
                    }

                    FlutterNativePageNames.CONTACT_US -> {
                        FlutterMiddleware.startFeedBack(applicationContext)
                    }

                    FlutterNativePageNames.VIP_RECHARGE -> {
                        val from = data?.get("from") as String?
                        val staticCover3d = data?.get("staticCover3d") as String?
                        val intent = Intent(applicationContext, VipRechargeActivity::class.java)
                        intent.putExtra("from", from)
                        intent.putExtra("staticCover3d", staticCover3d)
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        applicationContext.startActivity(intent)
                    }

                    FlutterNativePageNames.HOME -> {
                        RouterConstant.RouterMainActivity().go(applicationContext)
                    }

                    FlutterNativePageNames.COURSE_NOTES -> {
                        val bundle = Bundle()
                        bundle.putParcelable(
                            RouterConstant.RouterWebView.BEAN, WebBean(url = AppConstant.URL_CLAUSE, isShowTitle = true)
                        )
                        RouterConstant.RouterWebView().go(applicationContext, bundle)
                    }

                    FlutterNativePageNames.COURSE_START -> {
                        val id = data?.get("id") as String?
                        val cover = data?.get("cover") as String?
                        val equipTypeId = data?.get("equipTypeId") as String?
                        val noDevice = data?.get("noDevice") as Int?
                        RouterConstant.routerGo(
                            applicationContext, "VideoPlayerActivity", bundleOf(
                                "status" to 2,
                                "id" to id,
                                "cover" to cover,
                                "trainType" to 1,
                                "equipTypeId" to equipTypeId,
                                "noDevice" to (noDevice == 1),
                            )
                        )
                    }

                    FlutterNativePageNames.EXERCISE_REPORT_WEB -> {
                        val sportResultId = data?.get("sportResultId") as String?
                        if (sportResultId == null || sportResultId == "") {
                            return@onOpenNativePage
                        }
                        val bundle = bundleOf()
                        bundle.putParcelable(
                            RouterConstant.RouterWebView.BEAN, WebBean(
                                id = sportResultId, url = AppConstant.URL_TRAIN_REPORT, isNew = true
                            )
                        )
                        RouterConstant.RouterWebView().go(applicationContext, bundle)
                    }
                }
            }
        }
    }

    fun setDefaultOptions() {
        val option = option {
            baseUrl = AppConstant.getBaseUrl()
            baseH5Url = AppConstant.getBaseWebUrl()
            buildType = AppConstant.getFlutterBuildType()
            charlesIp = if (BuildConfig.IS_DEBUG) (MMKV.defaultMMKV().decodeString("SP_CHARLES_IP") ?: "") else ""
            unit = getCurrentUnit()
        }

        FlutterMiddleware.setOptions(option)
    }

    fun setLanguage(map: HashMap<String, String>) {
        FlutterMiddleware.setLanguage(map)
    }

    fun setHeader() {
        val map = java.util.HashMap<String, Any>()
        map["Authorization"] = MyApplication.mCommonViewModel.getToken()
        map["X-LANGUAGE-KEY"] = getNetLanguage()
        map["TimeZone"] = DateUtils.getTimeZone()
        map["terminal"] = 1
        map["User-Agent"] = java.lang.String.format("%s", MyApplication.instance.vbGetAppVersionCode().toString())
        map["App-Version"] = MyApplication.instance.vbGetAppVersionName()
        map["lang"] = getNetLanguage()
        map["server"] = getWebServer()
        FlutterMiddleware.setHeaders(map)
    }

}