package com.merit.common.utils

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.LogUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.merit.common.RouterConstant
import com.merit.common.bean.AppLinkBean
import com.merit.common.bean.WebBean
import com.v.log.util.log

object IntentApp {
    //  contentType:
    //    APP_LINK("app_link", "app站内跳转"),
    //    APP_H5("app_h5", "app站内webview"),
    //    APP_APP("app_app", "app跳站外app"),
    //    H5_APP("h5_app", "h5跳站内app"),
    //    H5_LINK("h5_link", "h5跳站内h5"),
    //    H5_H5("h5_h5", "h5跳站外h5");
    //    APP_OUTER_H5("app_outer_h5", "app站外浏览器"),
    fun intent(
        activity: FragmentActivity,
        contentType: String,
        content: String,
    ) {
        when (contentType) {
            "app_app" -> intentOtherApp(activity, content)
            "app_h5" -> {
                val bundle = Bundle()
                bundle.putParcelable(
                    RouterConstant.RouterWebView.BEAN, WebBean(url = content, isShowTitle = true)
                )
                RouterConstant.RouterWebView().go(activity, bundle)
            }

            "app_link" -> try {
                if (content.isEmpty()) {
                    return
                }
                try {
                    val gson = Gson()
                    val type = object : TypeToken<AppLinkBean>() {}.type
                    val appLinkBean: AppLinkBean = gson.fromJson(content, type)
                    goActivity(activity, appLinkBean.androidv2.name, appLinkBean.androidv2.arg)
                } catch (e: Exception) {
                    e.log()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            "app_outer_h5" -> {
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse(content.trim())
                }
                activity.startActivity(intent)
            }

            else -> {

            }
        }
    }

    private fun goActivity(context: Context, name: String, dataJson: Map<String, String>) {
        try {
            val clazz = Class.forName(name)
            val intent = Intent(context, clazz)
            intent.putExtras(dataJson.toBundle())
            context.startActivity(intent)
        } catch (e: ClassNotFoundException) {
            e.printStackTrace()
        }
    }

    private fun intentOtherApp(context: Context, url: String) {
        try {
            val intent = Intent()
            intent.action = Intent.ACTION_VIEW
            val uri = Uri.parse(url)
            intent.data = uri
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            e.printStackTrace()
        }
    }
}