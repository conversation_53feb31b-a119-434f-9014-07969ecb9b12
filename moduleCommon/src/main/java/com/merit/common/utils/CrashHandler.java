package com.merit.common.utils;


import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.os.Environment;
import android.util.Log;

import com.hjq.language.MultiLanguages;
import com.merit.common.AppConstant;
import com.merit.common.BuildConfig;
import com.merit.common.MyApplication;
import com.merit.common.R;
import com.tencent.mmkv.MMKV;
import com.v.base.utils.BaseUtilKt;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;


/**
 * author  : ww
 * desc    :
 * time    : 2022/11/7 6:41 PM
 */
public class CrashHandler implements Thread.UncaughtExceptionHandler {

    private static final String TAG = "CrashHandler";
    /**
     * 系统默认的UncaughtException处理类
     */
    private Thread.UncaughtExceptionHandler mDefaultHandler;
    /**
     * 程序的Context对象
     */
    private Context mContext;
    /**
     * 错误报告文件的扩展名
     */
    private static final String CRASH_REPORTER_EXTENSION = ".txt";

    /**
     * CrashHandler实例
     */
    private static CrashHandler INSTANCE;

    /**
     * 保证只有一个CrashHandler实例
     */
    private CrashHandler() {
    }

    /**
     * 获取CrashHandler实例 ,单例模式
     */
    public static CrashHandler getInstance() {
        if (INSTANCE == null) {
            synchronized (CrashHandler.class) {
                if (INSTANCE == null) {
                    INSTANCE = new CrashHandler();
                }
            }
        }
        return INSTANCE;
    }

    /**
     * 初始化,注册Context对象,
     * 获取系统默认的UncaughtException处理器,
     * 设置该CrashHandler为程序的默认处理器
     *
     * @param ctx
     */
    public void init(Context ctx) {
        mContext = ctx;
        mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    /**
     * 当UncaughtException发生时会转入该函数来处理
     */
    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        handleException(ex);
        if (mDefaultHandler != null) {
            //收集完信息后，交给系统自己处理崩溃
            mDefaultHandler.uncaughtException(thread, ex);
        }
    }

    /**
     * 自定义错误处理,收集错误信息
     * 发送错误报告等操作均在此完成.
     * 开发者可以根据自己的情况来自定义异常处理逻辑
     */
    private void handleException(Throwable ex) {
        if (ex == null) {
            Log.w(TAG, "handleException--- ex==null");
            return;
        }
        String msg = ex.getLocalizedMessage();
        if (msg == null) {
            return;
        }
        //收集设备信息
        //保存错误报告文件
        saveCrashInfoToFile(ex);
    }


    /**
     * 保存错误信息到文件中
     *
     * @param ex
     * @return
     */
    public void saveCrashInfoToFile(Throwable ex) {
        Writer info = new StringWriter();
        PrintWriter printWriter = new PrintWriter(info);
        ex.printStackTrace(printWriter);
        Throwable cause = ex.getCause();
        while (cause != null) {
            cause.printStackTrace(printWriter);
            cause = cause.getCause();
        }
        String result = info.toString();
        printWriter.close();
        StringBuilder sb = new StringBuilder();
        @SuppressLint("SimpleDateFormat") SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
        String now = sdf.format(new Date());
        sb.append("\nTIME:").append(now);//崩溃时间
        //程序信息
        sb.append("\nVERSION_CODE:").append(BaseUtilKt.vbGetAppVersionName(mContext, mContext.getPackageName()));//软件版本号
        sb.append("\nCHANNEL:").append(CommonExtKt.getChannel());//渠道
        sb.append("\nBUILD_TYPE:").append(BuildConfig.BUILD_TYPE);//是否是DEBUG版本
        sb.append("\nURL:").append(AppConstant.INSTANCE.getBaseUrl());//请求的url
        sb.append("\nLOCATION:").append(MMKV.defaultMMKV().getString(AppConstant.MMKV_LOCATION, ""));//设备登录的定位信息
        sb.append("\nUNIT:").append(MMKV.defaultMMKV().getInt(AppConstant.MMKV_UNIT, 2));//保存的公英制单位 2英制 1公制
        sb.append("\nFIRST_APP:").append(MMKV.defaultMMKV().getBoolean(AppConstant.MMKV_FIRST_APP, true));//是否第一次打开app
        //语言
        sb.append("\nAPP_LANGUAGE:").append(MultiLanguages.getAppLanguage());//app语言
        sb.append("\nACTIVITY_LANGUAGE:").append(mContext.getResources().getString(R.string.string_current_language));//activity 语言
        sb.append("\nAPPLICATION_LANGUAGE:").append(MyApplication.instance.getResources().getString(R.string.string_current_language));//Application语言
        sb.append("\nSYSTEM_LANGUAGE:").append(MultiLanguages.getLanguageString(mContext, MultiLanguages.getSystemLanguage(), R.string.string_current_language));//系统语言
        sb.append("\nNET_LANGUAGE:").append(CommonExtKt.getNetLanguage());//网络请求头
        //设备信息
        sb.append("\nMANUFACTURER:").append(android.os.Build.MANUFACTURER);//品牌
        sb.append("\nMODEL:").append(android.os.Build.MODEL);//型号
        sb.append("\nRELEASE:").append(Build.VERSION.RELEASE);//sdk版本
        sb.append("\nSDK:").append(Build.VERSION.SDK_INT);//对应api
        sb.append("\nEXCEPTION:").append(ex.getLocalizedMessage());
        sb.append("\nSTACK_TRACE:").append(result);


        try {
            //当在创建时加入true参数，回实现对文件的续写。 false则会覆盖前面的数据
//            FileWriter writer = new FileWriter(getFileName(), true);
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(getFileName(), true), "UTF-8"));
            writer.write(sb.toString());
            writer.flush();
            writer.close();

        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, e.toString());

        }
    }

    public String getFileName() {
        SimpleDateFormat sDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String date = sDateFormat.format(new Date());
        String fileName = getCrashFilePath(mContext) + date + CRASH_REPORTER_EXTENSION;
        return fileName;
    }


    private String getCrashFilePath(Context context) {
        String path = null;
        try {
            if (Environment.MEDIA_MOUNTED.equals(Environment.MEDIA_MOUNTED) || !Environment.isExternalStorageRemovable()) {//如果外部储存可用
                path = context.getExternalFilesDir(null).getPath() + "/crash/";//获得外部存储路径,默认路径为 /storage/emulated/0/Android/data/包名/files/Logs/log_2016-03-14_16-15-09.log
            } else {
                path = context.getFilesDir().getPath() + File.separator + "Crash/";//直接存在/data/data里，非root手机是看不到的
            }
            File file = new File(path);
            if (!file.exists()) {
                file.mkdirs();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.e(TAG, "getCrashFilePath: " + path);
        return path;
    }
}
