package com.merit.common.utils

import androidx.health.connect.client.HealthConnectClient
import androidx.health.connect.client.permission.HealthPermission
import androidx.health.connect.client.records.ActiveCaloriesBurnedRecord
import androidx.health.connect.client.records.BodyFatRecord
import androidx.health.connect.client.records.DistanceRecord
import androidx.health.connect.client.records.ExerciseSessionRecord
import androidx.health.connect.client.records.HeightRecord
import androidx.health.connect.client.records.WeightRecord
import androidx.health.connect.client.records.metadata.Metadata
import androidx.health.connect.client.request.ReadRecordsRequest
import androidx.health.connect.client.time.TimeRangeFilter
import androidx.health.connect.client.units.Energy
import androidx.health.connect.client.units.Length
import androidx.health.connect.client.units.Mass
import com.merit.common.MyApplication
import com.merit.common.bean.SettlementSuccessBean
import com.merit.common.commonViewModel
import com.mrk.network.net.MrkNetwork
import com.v.log.util.log
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.Locale


/**
 * <AUTHOR>
 * desc:
 * @time   2024/8/13
 */
object HealthConnectUtil {
    val weightReadPermission = setOf(
        HealthPermission.getReadPermission(WeightRecord::class),
    )
    val weightWritePermission = setOf(
        HealthPermission.getWritePermission(WeightRecord::class),
    )
    val heightReadPermission = setOf(
        HealthPermission.getReadPermission(HeightRecord::class),
    )
    val heightWritePermission = setOf(
        HealthPermission.getWritePermission(HeightRecord::class),
    )
    val bodyFatPermission = setOf(
        HealthPermission.getReadPermission(BodyFatRecord::class),
    )
    val exercisePermission = setOf(
        HealthPermission.getWritePermission(ExerciseSessionRecord::class),
        HealthPermission.getWritePermission(ActiveCaloriesBurnedRecord::class),
        HealthPermission.getWritePermission(DistanceRecord::class),
    )

    val allPermissions =
        weightReadPermission + weightWritePermission + heightReadPermission + heightWritePermission + bodyFatPermission + exercisePermission

    private suspend fun hasAllPermissions(permissions: Set<String>): Boolean {
        val healthConnectClient = HealthConnectClient.getOrCreate(MyApplication.instance)
        return healthConnectClient.permissionController.getGrantedPermissions().containsAll(permissions)
    }

    fun insertHealthData(settlementSuccessBean: SettlementSuccessBean) {
        val healthConnectClient = HealthConnectClient.getOrCreate(MyApplication.instance)
        //    1 动感单车
        //    2 跑步机
        //    5 划船机
        //    6 椭圆机
        //    11 力量站
        val exerciseType = when (settlementSuccessBean.productId) {
            "1" -> ExerciseSessionRecord.EXERCISE_TYPE_BIKING_STATIONARY
            "2" -> ExerciseSessionRecord.EXERCISE_TYPE_RUNNING_TREADMILL
            "5" -> ExerciseSessionRecord.EXERCISE_TYPE_ROWING_MACHINE
            "6" -> ExerciseSessionRecord.EXERCISE_TYPE_ELLIPTICAL
            "11" -> ExerciseSessionRecord.EXERCISE_TYPE_STRENGTH_TRAINING
            else -> {
                ExerciseSessionRecord.EXERCISE_TYPE_OTHER_WORKOUT
            }
        }
        GlobalScope.launch {
            try {
                if (hasAllPermissions(exercisePermission)) {
                    try {
                        val zoneId = ZoneId.systemDefault() // 使用系统默认时区
                        val start = Instant.ofEpochMilli(settlementSuccessBean.startTimestamp.toLong()).atZone(zoneId)
                            .withNano(0)
                        val end =
                            Instant.ofEpochMilli(settlementSuccessBean.endTimestamp.toLong()).atZone(zoneId).withNano(0)
                        healthConnectClient.insertRecords(
                            listOf(
                                ExerciseSessionRecord(
                                    startTime = start.toInstant(),
                                    startZoneOffset = start.offset,
                                    endTime = end.toInstant(),
                                    endZoneOffset = end.offset,
                                    exerciseType = exerciseType,
                                    metadata = Metadata(clientRecordId = settlementSuccessBean.id)
                                ),
                                ActiveCaloriesBurnedRecord(
                                    startTime = start.toInstant(),
                                    startZoneOffset = start.offset,
                                    endTime = end.toInstant(),
                                    endZoneOffset = end.offset,
                                    energy = Energy.kilocalories(settlementSuccessBean.kcal.toDouble()),
                                    metadata = Metadata(clientRecordId = settlementSuccessBean.id)
                                ),
                                DistanceRecord(
                                    startTime = start.toInstant(),
                                    startZoneOffset = start.offset,
                                    endTime = end.toInstant(),
                                    endZoneOffset = end.offset,
                                    distance = Length.meters(settlementSuccessBean.distance.toDouble()),
                                    metadata = Metadata(clientRecordId = settlementSuccessBean.id)
                                ),
                            )
                        )
                        "HealthConnectUtil 插入运动成功 kcal:${settlementSuccessBean.kcal} distance:${settlementSuccessBean.distance}".log()
                    } catch (e: Exception) {
                        "HealthConnectUtil 插入运动异常 $e".log()
                        reportError(settlementSuccessBean.id, e)
                    }
                } else {
                    reportNoPermission(settlementSuccessBean.id)
                    "HealthConnectUtil 插入运动无权限".log()
                }
            } catch (e: Exception) {
                "HealthConnectUtil 插入运动权限异常 $e".log()
                reportError(settlementSuccessBean.id, e)
            }
        }
    }

    fun insertWeight(weightInput: Double?) {
        if (weightInput == null) {
            return
        }
        "HealthConnectUtil 开始插入体重 $weightInput".log()
        val healthConnectClient = HealthConnectClient.getOrCreate(MyApplication.instance)
        GlobalScope.launch {
            try {
                if (hasAllPermissions(weightWritePermission)) {
                    val time = ZonedDateTime.now().withNano(0)
                    val weightRecord = WeightRecord(
                        weight = Mass.kilograms(weightInput),
                        time = time.toInstant(),
                        zoneOffset = time.offset,
                    )
                    val records = listOf(weightRecord)
                    try {
                        healthConnectClient.insertRecords(records)
                        "HealthConnectUtil 插入体重成功".log()
                    } catch (e: Exception) {
                        "HealthConnectUtil 插入体重异常 $e".log()
                    }
                } else {
                    "HealthConnectUtil 插入体重无权限".log()
                }
            } catch (e: Exception) {
                "HealthConnectUtil 插入体重权限异常 $e".log()
            }
        }
    }

    fun insertHeight(heightInput: Double?) {
        if (heightInput == null) {
            return
        }
        "HealthConnectUtil 开始插入身高 $heightInput".log()
        val healthConnectClient = HealthConnectClient.getOrCreate(MyApplication.instance)
        GlobalScope.launch {
            try {
                if (hasAllPermissions(heightWritePermission)) {
                    val time = ZonedDateTime.now().withNano(0)
                    val weightRecord = HeightRecord(
                        height = Length.meters(heightInput / 100),
                        time = time.toInstant(),
                        zoneOffset = time.offset,
                    )
                    val records = listOf(weightRecord)
                    try {
                        healthConnectClient.insertRecords(records)
                        "HealthConnectUtil 插入身高成功".log()
                    } catch (e: Exception) {
                        "HealthConnectUtil 插入身高异常 $e".log()
                    }
                } else {
                    "HealthConnectUtil 插入身高无权限".log()
                }
            } catch (e: Exception) {
                "HealthConnectUtil 插入身高权限异常 $e".log()
            }
        }
    }

    fun readUserDataFromHealth() {
        "HealthConnectUtil 开始读取身体数据".log()
        GlobalScope.launch {
            try {
                val healthConnectClient = HealthConnectClient.getOrCreate(MyApplication.instance)
                if (hasAllPermissions(weightReadPermission + heightReadPermission + bodyFatPermission)) {
                    val startOfDay = ZonedDateTime.now().truncatedTo(ChronoUnit.DAYS)
                    val end = Instant.now()
                    val start = startOfDay.minusDays(30).toInstant()
                    try {
                        val map = mutableMapOf<String, Any>()
                        val weightRequest = ReadRecordsRequest(
                            recordType = WeightRecord::class, timeRangeFilter = TimeRangeFilter.between(start, end)
                        )
                        val weight =
                            healthConnectClient.readRecords(weightRequest).records.lastOrNull()?.weight?.inKilograms
                        if (weight != null && weight != commonViewModel.userBean.value?.userHealth?.weight?.toDouble()) {
                            map["weight"] = String.format(Locale.US, "%.1f", weight)
                        }

                        val heightRequest = ReadRecordsRequest(
                            recordType = HeightRecord::class, timeRangeFilter = TimeRangeFilter.between(start, end)
                        )
                        val height =
                            healthConnectClient.readRecords(heightRequest).records.lastOrNull()?.height?.inMeters ?: 0.0
                        val heightData = height * 100.0
                        if (height != 0.0 && heightData != commonViewModel.userBean.value?.userHealth?.height?.toDouble()) {
                            map["height"] = String.format(Locale.US, "%.1f", heightData)
                        }
                        val bodyFatRecordRequest = ReadRecordsRequest(
                            recordType = BodyFatRecord::class, timeRangeFilter = TimeRangeFilter.between(start, end)
                        )
                        val bodyFatRate =
                            healthConnectClient.readRecords(bodyFatRecordRequest).records.lastOrNull()?.percentage?.value
                                ?: 0.0
                        if (bodyFatRate != 0.0) {
                            map["bodyFatRate"] = bodyFatRate
                        }
                        map["metricModel"] = 1
                        "HealthConnectUtil 身体数据$map".log()
                        updateUserInfo(map)
                    } catch (e: Exception) {
                        "HealthConnectUtil 读取身体数据异常$e".log()
                    }
                } else {
                    "HealthConnectUtil 无身体权限".log()
                }
            } catch (e: Exception) {
                "HealthConnectUtil 读取身体数据权限异常$e".log()
            }
        }
    }

    private fun updateUserInfo(map: Map<String, Any>) {
        GlobalScope.launch {
            runCatching {
                MrkNetwork.instance.post("/user/health", map)
            }
        }
    }

    private fun reportNoPermission(sportResultId: String) {
        GlobalScope.launch {
            runCatching {
                MrkNetwork.instance.post(
                    "/sport/external-sync-log", mapOf(
                        "sportResultId" to sportResultId,
                        "reason" to 1,
                    )
                )
            }
        }
    }

    private fun reportError(sportResultId: String, e: Exception) {
        GlobalScope.launch {
            runCatching {
                MrkNetwork.instance.post(
                    "/sport/external-sync-log", mapOf(
                        "sportResultId" to sportResultId, "reason" to 0, "message" to e.message.toString()
                    )
                )
            }
        }
    }
}