package com.merit.common.utils

import android.content.Context

/**
 * 获取状态栏高度
 */
fun Context.vbGetStatusBarHeight(): Int = run {
    var result = -1
    val resourceId = this.resources.getIdentifier("status_bar_height", "dimen", "android")
    if (resourceId > 0) {
        result = this.resources.getDimensionPixelSize(resourceId)
    }
    if (result <= 35) {
        result = 123
    }
    return result
}