package com.merit.common.utils

import android.graphics.Color
import com.merit.common.utils.TextViewColorUtils
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.Spanned
import android.text.style.ClickableSpan
import android.text.TextPaint
import android.view.View
import android.widget.TextView
import java.util.regex.Pattern

/**
 * author  : ww
 * desc    :
 * time    : 2022/6/27 4:17 下午
 */
object TextViewColorUtils {
//    /**
//     * 获取带颜色的文本，设定关键字颜色，这里只接受一个关键字，并且没有点击事件
//     *
//     * @param originText 原始文本
//     * @param keyword    需要颜色的文字
//     * @param color      颜色
//     * @return CharSequence 处理后的文字
//     */
//    fun getColorString(originText: String, keyword: String, color: Int): CharSequence {
//        return getColorString(originText, keyword, color, null)
//    }
//
//    /**
//     * 获取带颜色的文本，将给定的元是字符串
//     *
//     * @param originText 原始文本
//     * @param keyword    关键字
//     * @param color      颜色
//     * @param listener   点击关键字的监听回调，可空
//     * @return
//     */
//    fun getColorString(
//        originText: String, keyword: String, color: Int,
//        onClick: (Int) -> Unit?,
//    ): CharSequence {
//        val s = SpannableString(originText)
//        val p = Pattern.compile(keyword)
//        val m = p.matcher(s)
//        var index = 0
//        while (m.find()) {
//            val start = m.start()
//            val end = m.end()
//            s.setSpan(ForegroundColorSpan(color), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
//            if (onClick != null) {
//                s.setSpan(object : ClickableSpan() {
//                    override fun onClick(widget: View) {
//                        onClick.invoke(index)
//                        index++
//                    }
//
//                    override fun updateDrawState(ds: TextPaint) {
//                        ds.color = color
//                        ds.isUnderlineText = false
//                    }
//                }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
//
//            }
//
//        }
//        return s
//    }
//
//    fun TextView.getColorString(
//        vararg keys: String,
//        color: String = "#FF2451", onClick: (Int) -> Unit?,
//    ): CharSequence {
//        if (this.text.isNullOrEmpty())
//        {
//            return ""
//        }
//        val s = SpannableString(this.text)
//        for (i in keys.indices) {
//            val keyword = keys[i]
//            val p = Pattern.compile(keyword)
//            val m = p.matcher(s)
//            while (m.find()) {
//                val start = m.start()
//                val end = m.end()
//                s.setSpan(ForegroundColorSpan(Color.parseColor(color)), start, end,
//                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
//                if (onClick != null) {
//                    s.setSpan(object : ClickableSpan() {
//                        override fun onClick(widget: View) {
//                            onClick.invoke(i)
//                        }
//
//                        override fun updateDrawState(ds: TextPaint) {
//                            ds.color = Color.parseColor(color)
//                            ds.isUnderlineText = false
//                        }
//                    }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
//
//                }
//            }
//        }
//        return s
//    }
}