package com.merit.common.utils

import android.content.res.AssetManager
import android.graphics.Color
import android.graphics.Typeface
import android.os.Build
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.databinding.BindingAdapter
import com.v.base.utils.vbDp2px2Float
import com.v.base.utils.vbGetString
import java.text.NumberFormat


/**
 * 数字字体
 * @param fontType 1正体 其他为斜体
 */
@BindingAdapter(value = ["vb_type_face_num"], requireAll = false)
fun TextView.vbTypeface(fontType: Int) {
//        1 -> "Montserrat-BoldItalic.ttf"  //粗斜体
//        2 -> "Montserrat-Regular.ttf" //正常
//        3 -> "Montserrat-Medium.ttf" //正常加粗
//        4 -> "Montserrat-SemiBold.ttf" //正常加粗粗
//        5 -> "Montserrat-Bold.ttf" //正常加粗粗粗
//        else -> "Montserrat-Regular.ttf" //正常
    this.typeface = context.getTextTypeface(fontType)
}


@BindingAdapter(value = ["vb_click_animator_no"], requireAll = false)
fun View.vbClickAnimatorNo(onClickListener: View.OnClickListener?) {
    this.setOnClickListener(onClickListener)
}

@BindingAdapter(value = ["vb_text_format_no_zero"], requireAll = false)
fun TextView.vbTextFormatNoZero(str: String) {
    try {
        this.text = NumberFormat.getInstance().format(str.toFloat())
    } catch (e: Exception) {
        this.text = str
    }
}


/**
 * 设置控件阴影
 */
@BindingAdapter(value = ["vb_shadow_color", "vb_shadow_color_elevation"], requireAll = false)
fun View.vbShadowColor(color: String?, ev: Int = 30) = run {
    color?.run {
        val e = ev.let {
            if (it <= 0)
                30
            else
                it
        }.apply {
            vbDp2px2Float()
        }.toFloat()
        elevation = e
        translationZ = e / 3f

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            outlineAmbientShadowColor = Color.parseColor(color)// 环境阴影
            outlineSpotShadowColor = Color.parseColor(color)// 点阴影
        }

    }

}


/**
 * 会加上传入的值
 * 身高体重英制公制单位扩展名显示
 */
@BindingAdapter(value = ["vb_unit_height", "vb_unit_weight"], requireAll = false)
fun TextView.vbUnit(height: Any?, weight: Any?) {
    height?.run {
        text = height.toString().getHeightFormat() + " " + getHeightUnit()
    }
    weight?.run {
        text = weight.toString() + " " + getWeightUnit()
    }

}


/**
 * 距离单位
 */
@BindingAdapter(value = ["vb_distance_unit"], requireAll = false)
fun TextView.vbDistanceUnit(resString: Int) {

    text = if (resString != -1) {
        String.format(
            context.vbGetString(resString),
            getDistanceUnit()
        )
    } else {
        getDistanceUnit()
    }
}


@BindingAdapter(value = ["vb_text_str","vb_text_id"], requireAll = false)
fun View.setTextString(key:String,str:String) {
    val str = StringManagerUtils.getString(key,str)
    if (this is TextView){
        this.text = str
    }else if (this is Button){
        this.text = str
    }

}

@BindingAdapter(value = ["vb_hint_str","vb_default"], requireAll = false)
fun EditText.setHintStr(key: String,str: String){
    val str = StringManagerUtils.getString(key,str)
    this.hint = str
}
