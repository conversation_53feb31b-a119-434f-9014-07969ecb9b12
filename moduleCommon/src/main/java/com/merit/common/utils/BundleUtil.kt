package com.merit.common.utils

import android.os.Bundle
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.ArrayList

/**
 * <AUTHOR>
 * desc:
 * @time   2024/7/30
 */
fun String.jsonToBundle(): Bundle {
    val gson = Gson()
    val type = object : TypeToken<Map<String, Any>>() {}.type
    val map: Map<String, Any> = gson.fromJson(this, type)
    return map.toBundle()
}

fun Map<String, Any>.toBundle(): Bundle {
    val bundle = Bundle()
    for ((key, value) in this) {
        when (value) {
            is String -> bundle.putString(key, value)
            is Int -> bundle.putInt(key, value)
            is Boolean -> bundle.putBoolean(key, value)
            is Double -> bundle.putDouble(key, value)
            is Long -> bundle.putLong(key, value)
            is Map<*, *> -> bundle.putBundle(key, (value as Map<String, Any>).toBundle())
            is FloatArray -> bundle.putFloatArray(key, value)
            is DoubleArray -> bundle.putDoubleArray(key, value)
            is IntArray -> bundle.putIntArray(key, value)
        }
    }
    return bundle
}