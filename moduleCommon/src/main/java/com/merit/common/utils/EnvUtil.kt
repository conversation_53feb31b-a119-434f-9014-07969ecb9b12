package com.merit.common.utils

import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.merit.common.BuildConfig
import com.merit.common.bean.EnvBean
import com.merit.common.bean.EnvironmentItem
import com.v.log.util.log
import okhttp3.OkHttpClient
import okhttp3.Request
import kotlin.concurrent.thread

/**
 * <AUTHOR>
 * desc:
 * @time   2025/6/26
 */
object EnvUtil {
    private var envBeanLiveData = MutableLiveData<EnvBean>()
    var envBean = envBeanLiveData.value

    fun init() {
        if (!BuildConfig.IS_DEBUG) {
            return
        }
        thread {
            val client = OkHttpClient()
            try {
                val request = Request.Builder().url("https://merit-app-test.merach.com/env.json").build()

                val response = client.newCall(request).execute() // 同步执行请求
                if (response.isSuccessful) { // 检查 HTTP 状态码是否为 2xx
                    val responseBody = response.body
                    if (responseBody != null) {
                        val content = responseBody.string() // 直接获取字符串（小文件适用）
                        content.log()
                        envBean = Gson().fromJson(content, EnvBean::class.java)
                    }
                } else {
                    // 处理请求失败（如 404、500 等）
                    "下载失败，状态码：${response.code}".log()
                }
            } catch (e: Exception) {
                // 处理网络异常（如超时、无网络等）
                e.printStackTrace()
                e.log()
            }
        }
    }

    fun getEnvName(id: Int): String {
        return envBean?.environments?.sea?.firstOrNull { it.id == id }?.name ?: "无环境名称"
    }

    fun getEnv(id: Int): EnvironmentItem? {
        return envBean?.environments?.sea?.firstOrNull { it.id == id }
    }

    fun getDefaultEnvId(): Int {
        return envBean?.environments?.let { env ->
            // 合并国内和海外环境列表（处理可能的 null 情况）
            val seaEnvs = env.sea ?: emptyList() // 如果 sea 为 null，返回空列表
            val allEnvs = seaEnvs       // 合并所有环境

            // 查找第一个 default 为 true 的环境项 ID
            allEnvs.firstOrNull { it.default }?.id ?: 0 // 未找到时返回 0
        } ?: 0 // envBean 为 null 时返回 0
    }
}