package com.merit.common.utils

import android.content.Context
import android.text.TextUtils
import com.merit.common.R
import com.merit.common.StringKeyConstant
import com.v.base.utils.vbGetString
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*


object DateUtils {
    /**
     * 秒数转时分秒
     */
    fun secondToHMS(time: Long): String {
        if (time == 0L) return "00:00:00"
        val hours = time / 3600
        val minute = (time - hours * 3600) / 60
        val second = time % 60
        val h = if (hours < 10) "0$hours" else hours.toString()
        val min = if (minute < 10) "0$minute" else minute.toString()
        val sec = if (second < 10) "0$second" else second.toString()
        return "$h:$min:$sec"
    }

    fun getSeconds(time: String): Int {
        if (time == "0") return 0
        val calendar: Calendar = getCalendar(time)
        return calendar[Calendar.SECOND] + calendar[Calendar.MINUTE] * 60 + calendar[Calendar.HOUR] * 3600
    }

    /**
     * 返回一个带有时间格式的日历对象
     */
    fun getCalendar(time: String): Calendar {
        val calendar = Calendar.getInstance()
        if (TextUtils.isEmpty(time) || time == "0") {
            calendar[0, 0, 0, 0, 0] = 0
            return calendar
        }
        val date1: Date = SimpleDateFormat("HH:mm:ss").parse(time)
        calendar.time = date1
        return calendar
    }

    /**
     * 秒数转分秒
     */
    fun secondToMS(time: Long): String {
        if (time <= 0) return "00:00"
        val minute = time / 60
        val second = time % 60
        val min = if (minute < 10) "0$minute" else minute.toString()
        val sec = if (second < 10) "0$second" else second.toString()
        return "$min:$sec"
    }

    /**
     * 获取当前时区的SimpleDate
     */
    private fun getDefaultTimeZone(pattern: String = "yyyy-MM-dd HH:mm:ss"): SimpleDateFormat {
        val simpleDate = SimpleDateFormat(pattern)
//        simpleDate.timeZone = TimeZone.getDefault()
        return simpleDate
    }

    /**
     * 获取当前时间戳
     */
    fun getCurrentString(pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
        val sd = getDefaultTimeZone(pattern)
        val date = Date(System.currentTimeMillis())
        return sd.format(date)
    }

    /**
     * 日期格式转换
     * @param time 传入的值 2022-11-16 17:57:09
     * @param customPattern 需要转换的日期格式 如yyyy-MM-dd
     *
     */
    fun getCustomDate(time: String, customPattern: String): String {
        var string = time
        val date = getDefaultTimeZone().parse(time)
        val sd = SimpleDateFormat(customPattern)
//        sd.timeZone = TimeZone.getDefault()
        string = sd.format(Date(date.time))

        return string
    }


    /**
     * 获取第几天的日期
     */
    fun getFutureDate(position: Int): String {
        val calendar = Calendar.getInstance()
        calendar[Calendar.DAY_OF_YEAR] = calendar[Calendar.DAY_OF_YEAR] - position
        val today = calendar.time
        return SimpleDateFormat("MM-dd").format(today)
    }

    /**
     * 获取前n天日期、后n天日期
     * @param str 需要传入的日期
     * @param distanceDay 前几天 如获取前7天日期则传-7即可；如果后7天则传7
     */
    fun getOldDate(str: String, distanceDay: Int): String {
        var result = str
        try {
            val dft = SimpleDateFormat("MM/dd")
            val beginDate = dft.parse(str)
            val date = Calendar.getInstance()
            date.time = beginDate
            date[Calendar.DATE] = date[Calendar.DATE] + distanceDay
            val endDate = dft.parse(dft.format(date.time))
            result = dft.format(endDate)
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return result
    }

    fun getMothDay(context: Context, str: String): String {
        var result = str
        try {
            val dft = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            val dftMM = SimpleDateFormat("MM/dd")
            val date = dft.parse(str)
            if (isToday(date)) {
                result= StringManagerUtils.getString(StringKeyConstant.data_today_title,R.string.string_today)
            } else {
                result = dftMM.format(date)
            }

        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return result
    }

    /**
     * 判断是否为今天
     */
    private fun isToday(date: Date): Boolean {
        // 获取当前时区
        val timeZone = TimeZone.getDefault()
        // 获取当前时间的Calendar对象
        val calendarNow = Calendar.getInstance(timeZone)
        // 获取传入时间的Calendar对象
        val calendarDate = Calendar.getInstance(timeZone)
        calendarDate.time = date

        // 判断是否为今天
        return (calendarNow[Calendar.YEAR] === calendarDate[Calendar.YEAR]
                && calendarNow[Calendar.DAY_OF_YEAR] === calendarDate[Calendar.DAY_OF_YEAR])
    }

    /**
     *  获取当前时区
     */
    fun getTimeZone(): String {
        val timeZone = TimeZone.getDefault()
//        val id = timeZone.getID(); //获取时区id，如“Asia/Shanghai”
//        val name = timeZone.getDisplayName(); //获取名字，如“中国标准时间”
//        val shotName = timeZone.getDisplayName(false, TimeZone.SHORT); //获取名字，如“GMT+08:00”
//        val time = timeZone.getRawOffset(); //获取时差，返回值毫秒  "28800000"
//
//        "$id   $name   $shotName   $time".log()
        return timeZone.id //获取时区id，如“Asia/Shanghai”
    }

}