package com.merit.common.utils

import com.merit.common.AppConstant
import com.merit.common.BuildConfig
import com.merit.common.R
import com.merit.common.StringKeyConstant
import com.merit.common.bean.ServerBean
import com.mrk.network.MrkNetworkConfig
import com.mrk.network.net.MrkNetwork
import com.mrk.network.net.NetworkApi
import com.tencent.mmkv.MMKV
import com.v.base.VBConfig


/**
 * author  : ww
 * desc    : 服务器
 * time    : 2021/12/20
 */
object NetServerUtil {

    /**
     * 获取服务器
     * @return 0日服 1美服 -1美服预发 -2日服预发 -3TestApi  -4UatApi
     */
    fun getNetServerStatus(): Int {
        if (BuildConfig.IS_DEBUG) {
            return MMKV.defaultMMKV().getInt(AppConstant.MMKV_NET_SERVER, EnvUtil.getDefaultEnvId())
        }
        return MMKV.defaultMMKV().getInt(AppConstant.MMKV_NET_SERVER, 1)
    }

    /**
     * 获取服务器对应文本
     */
    fun getNetServerString(): String {
        if (BuildConfig.IS_DEBUG) {
            return EnvUtil.getEnvName(getNetServerStatus())
        }
        return when (getNetServerStatus()) {
            1000 -> StringManagerUtils.getString(
                StringKeyConstant.page_servers_europe, R.string.page_servers_europe
            )

            0 -> StringManagerUtils.getString(
                StringKeyConstant.main_server_jp_bt, R.string.main_server_jp_bt
            )

            1 -> StringManagerUtils.getString(
                StringKeyConstant.main_server_us_bt, R.string.main_server_us_bt
            )

            else -> StringManagerUtils.getString(
                StringKeyConstant.main_server_us_bt, R.string.main_server_us_bt
            )
        }

    }

    fun getServerBeanList(status: Int): List<ServerBean> {
        val list = ArrayList<ServerBean>()


        //如果当前是debug环境 则添加测试服务器切换
        if (BuildConfig.IS_DEBUG) {
            EnvUtil.envBean?.environments?.sea?.forEach { env ->
                list.add(ServerBean(env.name, env.id, status == env.id))
            }
        } else {
            // 0日服 1美服 -1美服预发 -2日服预发 -3TestApi  -4UatApi
            list.add(
                ServerBean(
                    StringManagerUtils.getString(
                        StringKeyConstant.page_servers_europe, R.string.page_servers_europe
                    ), 1000, status == 1000
                )
            )
            list.add(
                ServerBean(
                    StringManagerUtils.getString(
                        StringKeyConstant.main_server_us_bt, R.string.string_change_server_usa
                    ), 1, status == 1
                )
            )
            list.add(
                ServerBean(
                    StringManagerUtils.getString(
                        StringKeyConstant.main_server_jp_bt, R.string.string_change_server_jp
                    ), 0, status == 0
                )
            )
        }
        return list
    }

    /**
     * 保存服务器
     * @param status //0日服 1美服
     */
    fun saveNetServerStatus(status: Int) {
        MMKV.defaultMMKV().putInt(AppConstant.MMKV_NET_SERVER, status)
        MrkNetworkConfig.options.baseUrl = AppConstant.getBaseUrl()
    }

}