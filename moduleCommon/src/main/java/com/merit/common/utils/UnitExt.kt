package com.merit.common.utils

import com.merit.common.AppConstant
import com.tencent.mmkv.MMKV
import com.v.base.utils.vbFormatDecimal
import com.v.log.util.log
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.math.pow
import kotlin.math.roundToInt
import kotlin.reflect.jvm.internal.impl.builtins.StandardNames.FqNames.number




/**
 * author  : ww
 * desc    : 所有单位转换
 * time    : 2022/9/5 7:19 下午
 */


/**
 *  获取保存的单位
 *  2英制 1公制
 */
fun getCurrentUnit(): Int {
    return MMKV.defaultMMKV().getInt(AppConstant.MMKV_UNIT, 2)
}


/**
 * 身高数据 格式转换
 */
fun String.getHeightFormat(unit: Int = getCurrentUnit()): String {
    //2英制 1公制
    //身高英制单位（ft/in），体重英制单位（lb）
    var str = this
    if (this.isNullOrEmpty()) {
        str = "0.0"
    }
    return if (unit == 2) {
        str.replace(".", "’") + "”"
    } else {
        str
    }
}


/**
 * 获取身高单位
 * @param isReserve 是否保留英制单位(有些地方不需要单位,直接在值上面以0’0"表示)
 */
fun getHeightUnit(isReserve: Boolean = true): String {
    //2英制 1公制
    //身高英制单位（ft/in），体重英制单位（lb）
    val unit = getCurrentUnit()
    return if (unit == 2) {
        if (isReserve) "ft/in" else ""
    } else {
        "cm"
    }

}


/**
 * 获取体重单位
 */
fun getWeightUnit(): String {
    //2英制 1公制
    //身高英制单位（ft/in），体重英制单位（lb）
    val unit = getCurrentUnit()
    return if (unit == 2) {
        "lb"
    } else {
        "kg"
    }
}

/**
 * 获取距离单位
 */
fun getDistanceUnit(): String {
    //2英制 1公制
    //身高英制单位（ft/in），体重英制单位（lb）
    val unit = getCurrentUnit()
    return if (unit == 2) {
        "mi"
    } else {
        "km"
    }
}


/**
 * 距离数据 格式转换
 * @param isKm 传入的数值是米还是公里 默认为米
 */
fun Double.getDistanceFormat(isKm: Boolean = false, length: Int = 2): String {

    //2英制 1公制
    //身高英制单位（ft/in），体重英制单位（lb）
    val status = getCurrentUnit()

    //如果传值已经是公里 则不需要做换算
    val number = if (isKm) {
        BigDecimal.valueOf(this)
    } else {
        //因为服务器返回的是m 所有要转换成公里
        BigDecimal.valueOf(this / 1000)
    }

    return UnitConvertEnum.convert(
        number,
        if (status == 2) UnitConvertEnum.INCH else UnitConvertEnum.METRIC,
        UnitPropEnum.DISTANCE
    ).toString().keepDecimals(length)
}


/**
 * 获取需要上传的单位
 */
fun getDevicePushUnit(deviceUnit: Int): String {
    //设备 0：公⾥，1：英⾥
    //本地 2英⾥ 1公⾥
    return when (deviceUnit) {
        0 -> "1"
        1 -> "2"
        else -> "-1"
    }
}

/**
 * 通过设备的上报类型 来转换当前需要显示的距离( 用户用户训练界面的展示)
 * @param netUnit 接口获取的单位 1公⾥ 2英⾥
 * @param deviceUnit 设备获取的单位 0：公⾥，1：英⾥
 */
fun Double.getDeviceDistanceFormat(netUnit: Int, deviceUnit: Int, isKm: Boolean = false): String {
//    "$netUnit  $deviceUnit $this".log("getDeviceDistanceFormat")
    return if (this <= 0.0) {
        "--"
    } else {
        //设备 0：公⾥，1：英⾥
        //本地 2英⾥ 1公⾥
        val unitVal = when (deviceUnit) {
            1 -> 1
            2 -> 2
            else -> netUnit
        }
//        "$netUnit  $deviceUnit $isKm  ${getCurrentUnit()}".log("getDeviceDistanceFormat")
        // 如果 设备单位和保存单位一样，直接展示
        if (unitVal == getCurrentUnit()) {
            if (isKm) {
                FloatUtils.getFloat(this, 1).toString()
            } else {
                FloatUtils.floatToString(this, 1000.0)
            }
        } else {
            "设备的距离的单位和本地的单位不一致  走本地数据转化".log("getDeviceDistanceFormat")
            this.getDistanceFormat(isKm)
        }
    }
}

fun Double.getDeviceDistanceFormatTwo(netUnit: Int, deviceUnit: Int, isKm: Boolean = false): String {
    "$netUnit  $deviceUnit $isKm  ${getCurrentUnit()}   $this".log("getDeviceDistanceFormat")
    return if (this <= 0.0) {
        "--"
    } else {
        //设备 0：公⾥，1：英⾥
        //本地 2英⾥ 1公⾥
        val unitVal = when (deviceUnit) {
            1 -> 1
            2 -> 2
            else -> netUnit
        }
//
        // 如果 设备单位和保存单位一样，直接展示
        if (unitVal == getCurrentUnit()) {
            FloatUtils.floatToString(this, 1000.0)
        } else {
            "设备的距离的单位和本地的单位不一致  走本地数据转化".log("getDeviceDistanceFormat")   // 1公里等于0.62137119英里
            //设备是公制数据
            if (unitVal == 1){
                BigDecimal.valueOf(this/1000).multiply(BigDecimal.valueOf(0.62137119)).toString().keepDecimals(2)
            }else{
                BigDecimal.valueOf(this/1000).multiply(BigDecimal.valueOf(1.609344)).toString().keepDecimals(2)
            }
            //this.getDistanceFormat(isKm)
        }
    }
}



/**
 * 接口数据 全部都要除1000 距离转换 格式转换
 */
fun Double.getDistanceNetFormat(): String {
    return FloatUtils.floatToString(this, 1000.0)
}

fun Double.getDistance():String{
    val a = (this/1000).toString()
    "a   ${a}".log("getDistance")
    if (a.contains(".")){
      val list =  a.split(".")
      return when(list[1].length){
          1->{
              "${a}0"
          }
          2->{
              a
          }
          else->{
              "${list[0]}.${list[1].substring(0,2)}"
          }
      }
    }else{
        return "${a}.00"
    }
}



fun Double.getDistanceFormat():String?{
    return FloatUtils.getFloat2((this/1000).toFloat())
}

/**
 * 目标训练(对应的目标转换成设备需要上报的)
 */
fun Double.getDeviceDistancePushFormat(currentUnit: Int): Double {
    return if (this <= 0.0) {
        0.0
    } else {
        (UnitConvertEnum.convert(
            BigDecimal.valueOf(this),
            if (currentUnit == 2) UnitConvertEnum.INCH else UnitConvertEnum.METRIC,
            UnitPropEnum.DISTANCE
        ).toString().keepDecimals(0).toDouble())
    }
}



//decimalPlaces 要保留的位数  此方法是保留的小数如果是大于0的值，直接往前进一位
fun Double.getRoundNumber(unit:Double,decimalPlaces:Int): String {
    val number = (this/unit).vbFormatDecimal(decimalPlaces+1,RoundingMode.HALF_UP)
    val decimalPart: Double = (number - number.toInt())
    val decimalMultiplier = 10.0.pow(decimalPlaces.toDouble())
    val lastDecimal = (decimalPart * decimalMultiplier).roundToInt() / decimalMultiplier
    "$number $decimalPart  $decimalMultiplier   $lastDecimal".log("getRoundNumber")
    val roundedNumber = if (lastDecimal > 0) {
        // 最后一位小数有值，进位
        ceil(number * decimalMultiplier) / decimalMultiplier
    } else {
        // 最后一位小数为0，保持原值
        number.vbFormatDecimal(decimalPlaces,RoundingMode.HALF_UP)
    }
    "roundedNumber::$roundedNumber".log("getRoundNumber")
    var formatted = "0.00"
    formatted = if (roundedNumber ==0.0){
        "0.00"
    }else {
        if (roundedNumber.toString().contains(".")){
            val list =  roundedNumber.toString().split(".")
            if (list[1].length==1){
                "${roundedNumber}0"
            }else{
                roundedNumber.toString()
            }
        }else{
            "${roundedNumber}.00"
        }
    }
    "$formatted".log("20231206")
    return  formatted
}




