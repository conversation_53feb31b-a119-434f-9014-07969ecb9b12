package com.merit.common.utils;


import android.util.Log;

import java.math.BigDecimal;
import java.math.RoundingMode;

public enum UnitConvertEnum {

    /**
     * 公制
     */
    METRIC(BigDecimal.valueOf(0.4535), BigDecimal.valueOf(30.48), BigDecimal.valueOf(1)),

    /**
     * 英制
     */
    INCH(BigDecimal.valueOf(2.2046), BigDecimal.valueOf(0.0328), BigDecimal.valueOf(0.6214));

    /**
     * 体重：kg - lb
     */
    private final BigDecimal weightProp;

    /**
     * 身高：cm - ft
     */
    private final BigDecimal ftProp;


    /**
     * 距离：m - mi
     */
    private final BigDecimal meterProp;


    public BigDecimal getWeightProp() {
        return weightProp;
    }

    public BigDecimal getFtProp() {
        return ftProp;
    }


    public BigDecimal getMeterProp() {
        return meterProp;
    }

    UnitConvertEnum(BigDecimal weightProp, BigDecimal ftProp, BigDecimal meterProp) {
        this.weightProp = weightProp;
        this.ftProp = ftProp;
        this.meterProp = meterProp;
    }

    public static BigDecimal convert(BigDecimal data, UnitConvertEnum unitConvertEnum, UnitPropEnum unitPropEnum) {

        BigDecimal prop = null;

        if (unitPropEnum == UnitPropEnum.KG) {
            prop = unitConvertEnum.getWeightProp();
        } else if (unitPropEnum == UnitPropEnum.HEIGHT) {  //此方法存在精度问题，因此使用下面的方法
            prop = unitConvertEnum.getFtProp();
        } else if (unitPropEnum == UnitPropEnum.DISTANCE) {
            prop = unitConvertEnum.getMeterProp();
        }
        return data.multiply(prop);
    }



    //因为身高长度的上面方法转换存在精度问题，因此涉及到高度的使用此方法。
    public static String convertHeight(String num,int status){
        if (status==1){
            int feet = Integer.parseInt(num.split("’")[0]);
            int inches = Integer.parseInt(num.split("”")[0].split("’")[1]);
            double totalLenthInInches = feet*12+inches;
            double result = totalLenthInInches *2.54;
            return  BigDecimal.valueOf(result).setScale(1, RoundingMode.HALF_UP).toString(); //此处必须只能保留一位小数，因为我的数据中的弹框会比较
        }else{
           double lengthInCentimeter = Double.valueOf(num);
           double lengthInInches = lengthInCentimeter/2.54;
           int feet = (int) (lengthInInches/12);
           int inches = (int)(lengthInInches%12);
           return feet + "’" + inches + "”";
        }
    }

}
