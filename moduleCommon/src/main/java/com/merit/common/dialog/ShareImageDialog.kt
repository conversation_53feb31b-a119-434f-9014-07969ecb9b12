package com.merit.common.dialog

import android.animation.IntEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.*
import android.widget.FrameLayout
import android.widget.RelativeLayout

import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.merit.common.R
import com.merit.common.adapter.ShareAdapter
import com.merit.common.bean.ShareBean
import com.merit.common.bean.ShareItemBean
import com.merit.common.databinding.DialogShareImageBinding
import com.merit.common.utils.BlurTransformation
import com.merit.common.utils.FileUtils
import com.v.base.annotaion.VBDialogOrientation
import com.v.base.dialog.VBDialog
import com.v.base.utils.*
import java.util.*
import kotlin.collections.ArrayList


/**
 * author  : ww
 * desc    : 分享图片
 * time    : 2022-01-15 16:14:10
 */
class ShareImageDialog(private var mContext: Context, private var itemBean: ShareItemBean) :
    VBDialog<DialogShareImageBinding>(mContext),
    View.OnClickListener {

    private val labelAdapter by lazy {
        mDataBinding.recyclerView.vbGrid(ShareAdapter(), 2) as ShareAdapter
    }


    override fun useDirection(): VBDialogOrientation {
        return VBDialogOrientation.BOTTOM
    }

    override fun initData() {
        mDataBinding.v = this

//        blurTransformation()
        animatorZoom()


        var listLabel = ArrayList<ShareBean>()


        labelAdapter.vbLoad(listLabel.toMutableList())
        labelAdapter.setOnItemClickListener { adapter, view, position ->
            val item = labelAdapter.data[position]
        }

    }


    //缩放动画
    private fun animatorZoom() {

        mDataBinding.ivCover.setImageBitmap(itemBean.bitmap)


        val lp: FrameLayout.LayoutParams =
            FrameLayout.LayoutParams(mDataBinding.layoutContent.layoutParams)

        val animator: ValueAnimator = ValueAnimator.ofObject(
            IntEvaluator(),
            0, 38.vbDp2px2Float()
        )
        animator.addUpdateListener { animation ->
            val margin = animation.animatedValue as Int
            lp.setMargins(margin, margin * 2, margin, margin * 2)
            mDataBinding.layoutContent.layoutParams = lp
        }
        animator.duration = 800
        animator.start()
    }

    //高斯模糊
    private fun blurTransformation() {

        Glide.with(mContext)
            .load("https://static.merach.com/coach/20211124/Z8pXbXAwvT2UxS1L_750x915.jpg")
            .centerCrop()
            .apply(RequestOptions.bitmapTransform(BlurTransformation(mContext, 25, 3)))
            .into(mDataBinding.ivBg)


    }

    override fun onClick(v: View) {
        when (v.id) {
            mDataBinding.ivClose.id -> {
                dismiss()
            }
        }
    }


}