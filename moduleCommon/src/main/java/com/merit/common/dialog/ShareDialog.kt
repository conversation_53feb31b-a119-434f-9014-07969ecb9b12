package com.merit.common.dialog

import android.content.Context
import android.graphics.Color
import android.view.View
import com.merit.common.R
import com.merit.common.adapter.ShareAdapter
import com.merit.common.bean.ShareBean
import com.merit.common.bean.ShareItemBean
import com.merit.common.databinding.DialogShareBinding
import com.v.base.annotaion.VBDialogOrientation
import com.v.base.dialog.VBDialog
import com.v.base.utils.vbGrid
import com.v.base.utils.vbLoad
import kotlin.collections.ArrayList


/**
 * author  : ww
 * desc    : 分享
 * time    : 2021-12-26 16:36:16
 */
class ShareDialog(context: Context, private var itemBean: ShareItemBean) :
    VBDialog<DialogShareBinding>(context),
    View.OnClickListener {

    override fun useDim(): Boolean {
        return true
    }

    override fun useDirection(): VBDialogOrientation {
        return VBDialogOrientation.BOTTOM
    }

    private val labelAdapter by lazy {
        mDataBinding.recyclerView.vbGrid(ShareAdapter(), 1) as ShareAdapter
    }


    override fun initData() {
        mDataBinding.v = this


        val listLabel = ArrayList<ShareBean>()


        labelAdapter.vbLoad(listLabel.toMutableList())
        labelAdapter.setOnItemClickListener { adapter, view, position ->
            val item = labelAdapter.data[position]
//            share(item.platform)
        }
    }


    override fun onClick(v: View) {
        when (v.id) {
            /*mDataBinding.tvCancel.id, */mDataBinding.ivClose.id -> {
            dismiss()
        }
        }
    }


}