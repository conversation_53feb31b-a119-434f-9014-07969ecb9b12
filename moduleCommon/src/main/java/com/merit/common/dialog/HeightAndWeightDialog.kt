package com.merit.common.dialog

import android.content.Context
import android.view.View
import com.bigkoo.pickerview.adapter.ArrayWheelAdapter
import com.merit.common.AppConstant
import com.merit.common.R
import com.merit.common.StringKeyConstant
import com.merit.common.bean.SelectItemBean
import com.merit.common.databinding.BaseDialogHeighAndWeighBinding
import com.merit.common.utils.StringManagerUtils
import com.merit.common.utils.getCurrentUnit
import com.merit.common.utils.getHeightUnit
import com.merit.common.utils.getWeightUnit
import com.tencent.mmkv.MMKV
import com.v.base.annotaion.VBDialogOrientation
import com.v.base.dialog.VBDialog
import com.v.base.utils.vbGetString


/**
 * author  : ww
 * desc    : 体重身高 滚轮
 * time    : 2022-08-26 11:24:31
 */
class HeightAndWeightDialog(mContext: Context) : VBDialog<BaseDialogHeighAndWeighBinding>(mContext),
    View.OnClickListener {

    private var listLeft = ArrayList<SelectItemBean>()
    private var listRight = ArrayList<SelectItemBean>()
    private var cancel: String = StringManagerUtils.getString(StringKeyConstant.pop_ota_update_cancel_bt,R.string.string_cancel)
    private var title: String = ""
    private var confirm: String = StringManagerUtils.getString(StringKeyConstant.glo_confirm_bt,R.string.string_d_confirm)
    private var selectLeftItemBean: SelectItemBean? = null
    private var selectRightItemBean: SelectItemBean? = null

    private var listener: ((itemLeft: SelectItemBean?, itemRight: SelectItemBean?) -> Unit)? = null

    override fun useDirection(): VBDialogOrientation {
        return VBDialogOrientation.BOTTOM
    }

    override fun initData() {
        mDataBinding.v = this

        mDataBinding.tvCancel.text = cancel
        mDataBinding.tvTitle.text = title
        mDataBinding.tvConfirm.text = confirm

        if (listLeft.size > 0) {
            initLeft()
        }

        if (listRight.size > 0) {
            initRight()
        }
    }

    private fun initLeft() {
        val list = ArrayList<String>()
        var selectIndex = 0

        for (i in 0 until listLeft.size) {
            val item = listLeft[i]
            list.add(item.string)

            if (item.isSelect) {
                selectIndex = i
                selectLeftItemBean = item
            }
        }

        mDataBinding.wheelViewLeft.setLineSpacingMultiplier(4f)
        mDataBinding.wheelViewLeft.setItemsVisibleCount(5)

        mDataBinding.wheelViewLeft.currentItem = selectIndex
        mDataBinding.wheelViewLeft.setCyclic(false)
        mDataBinding.wheelViewLeft.adapter = ArrayWheelAdapter(list)
        mDataBinding.wheelViewLeft.setOnItemSelectedListener { index ->
            selectLeftItemBean = listLeft[index]
        }

    }


    private fun initRight() {
        val list = ArrayList<String>()
        var selectIndex = 0

        for (i in 0 until listRight.size) {
            val item = listRight[i]
            list.add(item.string)

            if (item.isSelect) {
                selectIndex = i
                selectRightItemBean = item
            }
        }

        mDataBinding.wheelViewRight.visibility = View.VISIBLE
        mDataBinding.wheelViewRight.setLineSpacingMultiplier(4f)
        mDataBinding.wheelViewRight.setItemsVisibleCount(5)

        mDataBinding.wheelViewRight.currentItem = selectIndex
        mDataBinding.wheelViewRight.setCyclic(false)
        mDataBinding.wheelViewRight.adapter = ArrayWheelAdapter(list)
        mDataBinding.wheelViewRight.setOnItemSelectedListener { index ->
            selectRightItemBean = listRight[index]
        }

    }


    fun setItems(
        listLeft: ArrayList<SelectItemBean>,
        listRight: ArrayList<SelectItemBean> = ArrayList()
    ): HeightAndWeightDialog {
        this.listLeft.addAll(listLeft)
        this.listRight.addAll(listRight)
        return this
    }


    fun setContent(cancel: String, title: String, confirm: String): HeightAndWeightDialog {
        this.cancel = cancel
        this.title = title
        this.confirm = confirm
        return this
    }

    fun setClickListener(listener: ((itemLeft: SelectItemBean?, itemRight: SelectItemBean?) -> Unit)): HeightAndWeightDialog {
        this.listener = listener
        return this
    }


    fun heightDispose(str: String = ""): HeightAndWeightDialog {

        val selectStr = str.replace("’", ".").replace("”", "")

        //英制 最小最大值(整数)
        var minLeftNum = 2
        var minRightNum = 0


        //英制 最小最大值(小数)
        var maxLeftNum = 8
        var maxRightNum = 11


        //英制 默认选中
        var leftOpSelect = maxLeftNum / 2//整数
        var rightOpSelect = 0//小数

        //2英制 1公制
        //身高英制单位（ft/in），体重英制单位（lb）
        val unit = getCurrentUnit()
        if (unit == 1) {
            //公制默认选中 (cm)

            minLeftNum = 60
            minRightNum = 0

            maxLeftNum = 274
            maxRightNum = 9


            leftOpSelect = maxLeftNum / 2
            rightOpSelect = 0
        }

        if (!selectStr.isNullOrEmpty()) {

            if (selectStr.contains(".")) {
                val str = selectStr.split(".")
                leftOpSelect = str[0].toInt()
                rightOpSelect = str[1].toInt()
            } else {
                leftOpSelect = selectStr.toInt()
                rightOpSelect = 0
            }
        }

        val listLeft: ArrayList<SelectItemBean> = ArrayList()
        for (i in minLeftNum..maxLeftNum) {
            listLeft.add(SelectItemBean(i.toString(), i.toString(), 0, leftOpSelect == i))
        }

        if (leftOpSelect < minLeftNum) {
            listLeft[0].isSelect = true
        }

        if (leftOpSelect > maxLeftNum) {
            listLeft[listLeft.size - 1].isSelect = true
        }


        val listRight: ArrayList<SelectItemBean> = ArrayList()
        for (i in minRightNum..maxRightNum) {
            listRight.add(SelectItemBean(i.toString(), i.toString(), 0, rightOpSelect == i))
        }

        if (rightOpSelect < minRightNum) {
            listRight[0].isSelect = true
        }

        if (rightOpSelect > maxRightNum) {
            listRight[listLeft.size - 1].isSelect = true
        }


        title = StringManagerUtils.getString(StringKeyConstant.main_height_tx,R.string.string_m_height) + "(" + getHeightUnit() + ")"
        setItems(listLeft, listRight)
        return this
    }


    fun weightDispose(selectStr: String = ""): HeightAndWeightDialog {

        //英制 默认选中

        //英制 最小最大值(整数)
        var minLeftNum = 56
        var minRightNum = 0


        //英制 最小最大值(小数)
        var maxLeftNum = 549
        var maxRightNum = 9


        var leftOpSelect = maxLeftNum / 2
        var rightOpSelect = 0


        //2英制 1公制
        //体重英制单位（lb）
        val unit = getCurrentUnit()
        if (unit == 1) {
            //公制默认选中 (kg)

            minLeftNum = 25
            minRightNum = 0

            maxLeftNum = 249
            maxRightNum = 9

            leftOpSelect = maxLeftNum / 2
            rightOpSelect = 0
        }

        if (!selectStr.isNullOrEmpty()) {

            if (selectStr.contains(".")) {
                val str = selectStr.split(".")
                leftOpSelect = str[0].toInt()
                rightOpSelect = str[1].toInt()
            } else {
                leftOpSelect = selectStr.toInt()
                rightOpSelect = 0
            }
        }

        val listLeft: ArrayList<SelectItemBean> = ArrayList()
        for (i in minLeftNum..maxLeftNum) {
            listLeft.add(SelectItemBean(i.toString(), i.toString(), 0, leftOpSelect == i))
        }


        if (leftOpSelect < minLeftNum) {
            listLeft[0].isSelect = true
        }

        if (leftOpSelect > maxLeftNum) {
            listLeft[listLeft.size - 1].isSelect = true
        }


        val listRight: ArrayList<SelectItemBean> = ArrayList()
        for (i in minRightNum..maxRightNum) {
            listRight.add(SelectItemBean(i.toString(), i.toString(), 0, rightOpSelect == i))
        }

        if (rightOpSelect < minRightNum) {
            listRight[0].isSelect = true
        }

        if (rightOpSelect > maxRightNum) {
            listRight[listLeft.size - 1].isSelect = true
        }


        title =StringManagerUtils.getString(StringKeyConstant.main_weight_tx,R.string.string_m_weight)+ "(" + getWeightUnit() + ")"
        setItems(listLeft, listRight)
        return this
    }

    override fun onClick(view: View) {
        when (view.id) {
            mDataBinding.tvCancel.id -> {
                dismiss()
            }
            mDataBinding.tvConfirm.id -> {
                listener?.invoke(selectLeftItemBean, selectRightItemBean)
                dismiss()

            }
        }
    }


}