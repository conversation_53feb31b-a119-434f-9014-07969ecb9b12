package com.merit.common.dialog

import android.content.Context
import android.view.Gravity
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.merit.common.R
import com.merit.common.bean.SelectItemBean
import com.merit.common.databinding.BaseDialogSelectBinding
import com.merit.common.databinding.BaseDialogSelectItemBinding
import com.v.base.annotaion.VBDialogOrientation
import com.v.base.dialog.VBDialog
import com.v.base.utils.vbDivider
import com.v.base.utils.vbDrawable
import com.v.base.utils.vbLinear


/**
 * author  : ww
 * desc    : 列表选择
 * time    : 2022-01-10 16:24:31
 */
class SelectItemDialog(mContext: Context) : VBDialog<BaseDialogSelectBinding>(mContext),
    View.OnClickListener {

    private var list = ArrayList<SelectItemBean>()
    private var title: String = ""

    private var listener: ((dialog: SelectItemDialog, item: SelectItemBean) -> Unit)? =
        null

    override fun useDirection(): VBDialogOrientation {
        return VBDialogOrientation.BOTTOM
    }

    private val mAdapter by lazy {
        mDataBinding.recyclerView.vbLinear(MyAdapter()).vbDivider {
            setMargin(1)
            setColor("#E2E4EA")
        } as MyAdapter
    }


    override fun initData() {
        mDataBinding.v = this
        mAdapter.setList(list)
        mAdapter.setOnItemClickListener { adapter, view, position ->
            listener?.run {

                mAdapter.getItem(position).isSelect = true
                invoke(
                    this@SelectItemDialog,
                    mAdapter.getItem(position))

            }

        }

        mDataBinding.tvTitle.text = title


    }


    fun setItems(items: ArrayList<SelectItemBean>): SelectItemDialog {
        this.list.addAll(items)
        return this
    }


    fun setTitle(title: String): SelectItemDialog {
        this.title = title
        return this
    }

    fun setClickListener(listener: ((dialog: SelectItemDialog, item: SelectItemBean) -> Unit)): SelectItemDialog {
        this.listener = listener
        return this
    }


    class MyAdapter :
        BaseQuickAdapter<SelectItemBean, BaseDataBindingHolder<BaseDialogSelectItemBinding>>(R.layout.base_dialog_select_item) {

        override fun convert(
            holder: BaseDataBindingHolder<BaseDialogSelectItemBinding>,
            item: SelectItemBean,
        ) {
            holder.dataBinding?.run {
                tvContent.text = item.string

                if (item.icon == 0) {
                    tvContent.gravity = Gravity.CENTER
                    tvContent.vbDrawable(0, null, 0, 0)
                } else {
                    tvContent.gravity = Gravity.LEFT
                    tvContent.vbDrawable(item.icon, null, 18, 18)
                }
                executePendingBindings()
            }
        }

    }

    override fun onClick(view: View) {
        when (view.id) {
            mDataBinding.tvCancel.id -> {
                dismiss()
            }

        }
    }


}