package com.merit.common.net

import com.merit.common.MyApplication
import com.merit.common.R
import com.merit.common.RouterConstant
import com.merit.common.StringKeyConstant
import com.merit.common.utils.StringManagerUtils
import com.mrk.network.net.MrkAppException
import com.mrk.network.net.MrkExceptionHandling
import com.v.log.util.logE

/**
 * author  : ww
 * desc    : 网络返回的异常处理
 * time    : 2024/10/21 20:09
 */
class NetworkExceptionHandling : MrkExceptionHandling() {

    override fun onException(throwable: Throwable): String {
        var msg = ""
        val errorLog = throwable.toString()

        when (throwable) {
            is MrkAppException -> {
                msg = throwable.errorMsg
                when (throwable.errCode) {
                    //502 Gateway Timeout
                    500, 502, 503, 504 -> {
                        msg =  StringManagerUtils.getString(
                            StringKeyConstant.course_play_error_tip,
                            R.string.course_play_error_tip
                        )+"(${throwable.errCode})"
                    }
                    //异地登录
                    401, 403 -> {
                        msg = ""
                        RouterConstant.RouterLogin().go(MyApplication.instance)
                    }

                    else -> {
                    }
                }
            }

            else -> {
                msg = throwable.toString()
            }
        }
        (errorLog+"\n"+msg).logE("BaseViewModel")
        return msg
    }
}
