package com.merit.common.net

import com.merit.common.MyApplication
import com.merit.common.commonViewModel
import com.merit.common.utils.DateUtils
import com.merit.common.utils.getNetLanguage
import com.v.base.utils.vbGetAppVersionCode
import com.v.base.utils.vbGetAppVersionName
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.lang.String

class NetworkHeadInterceptor : Interceptor {

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val builder = chain.request().newBuilder()
        builder.addHeader("Content-Type", "application/json").addHeader("Authorization", commonViewModel.getToken())
            .addHeader("X-LANGUAGE-KEY", getNetLanguage()).addHeader("TimeZone", DateUtils.getTimeZone())
            .addHeader("terminal", "1").addHeader(
                "User-Agent", String.format("%s", MyApplication.instance.vbGetAppVersionCode().toString())
            ).addHeader("App-Version", MyApplication.instance.vbGetAppVersionName())
        return chain.proceed(builder.build())
    }

}