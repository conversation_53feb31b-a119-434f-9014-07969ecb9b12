package com.merit.common.viewmodel

import androidx.lifecycle.MutableLiveData
import com.merit.common.AppConstant
import com.merit.common.bean.ActivitiesBean
import com.merit.common.bean.LoginBean
import com.merit.common.bean.PreReportData
import com.merit.common.bean.UserBean
import com.merit.common.commonViewModel
import com.mrk.network.net.MrkNetwork
import com.mrk.network.net.request
import com.tencent.mmkv.MMKV
import com.v.base.utils.vbToBean


/**
 * author  : ww
 * desc    : 全局ViewModel
 * time    : 2021/12/20
 */
class CommonViewModel : BaseViewModel() {

    //登录数据
    var loginBean = MutableLiveData<LoginBean>()

    //用户数据
    var userBean = MutableLiveData<UserBean>()

    //预生成训练报告
    var mPreReportData = MutableLiveData<PreReportData>()


    init {
        val string = MMKV.defaultMMKV().getString(AppConstant.MMKV_LOGIN, "")
        if (!string.isNullOrEmpty()) {
            loginBean.value = (string.vbToBean(LoginBean::class.java))
        }
    }

    fun getToken(): String {
        var token = ""
        if (loginBean.value == null) {
            val string = MMKV.defaultMMKV().getString(AppConstant.MMKV_LOGIN, "")
            if (!string.isNullOrEmpty()) {
                val bean = string.vbToBean(LoginBean::class.java)
                loginBean.postValue(bean)
                token = bean.token
            }
        } else {
            token = loginBean.value?.token!!
        }
        return token
    }


    fun getUserInfo() {
        request(
            {
                MrkNetwork.instance.get("/user/info/v2")
            }, commonViewModel.userBean
        )
    }


    /**
     * 判断是否有送VIP活动
     * A00001
     */
    fun checkActivity(onSuccess: () -> Unit, onError: () -> Unit) {
        request<ActivitiesBean>(
            {
                MrkNetwork.instance.get(
                    "/user/activity/list"
                )
            }, success = {
                if (isActivityInCurrentTime(it.activities, "A00001")) {
                    onSuccess.invoke()
                } else {
                    onError.invoke()
                }
            }, error = {
                onError.invoke()
            }
        )
    }

    private fun isActivityInCurrentTime(
        activities: List<ActivitiesBean.Activity>, code: String
    ): Boolean {
        val currentTime = System.currentTimeMillis()

        // 遍历活动列表，检查是否存在匹配的活动
        for (bean in activities) {
            if (bean.activityCode == code) {
                // 检查活动是否在当前时间内有效
                if (currentTime <= bean.expireTimestamp) {
                    return true
                }
            }
        }
        return false
    }
}