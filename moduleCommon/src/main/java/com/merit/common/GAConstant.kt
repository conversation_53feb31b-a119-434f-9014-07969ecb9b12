package com.merit.common

/**
 * <AUTHOR>
 * desc:
 * @time   2023/11/28
 */
object GAConstant {
    object HOME {
        const val ULTRABURNING_BANNER_CLICK = "home_ultraburning_banner_click"
        const val FREE_TRAIN_BANNER_CLICK = "home_free_train_banner_click"
        const val TIMING_BANNER_CLICK = "home_timing_banner_click"
        const val FIXED_DISTANCE_BANNER_CLICK = "home_fixed_distance_banner_click"
        const val LIVE_VIDEO_COVER_CLICK = "home_live_video_cover_click"
        const val LIVE_VIDEO_RANK_CLICK = "home_live_video_rank_click"
        const val LIVE_VIDEO_SEEALL_CLICK = "home_live_video_seeall_click"
        const val LIVE_VIDEO_CHALLENGE_CLICK = "home_live_video_challenge_click"
        const val ADD_DEVICE_CLICK = "home_add_device_click"
        const val MULTI_DEVICE_ALERT = "home_multi_device_alert"

        object ULTRA {
            const val FILTER_CLICK = "home_ultraburning_filter_click"
            const val DEVICETYPE_CLICK = "home_ultraburning_devicetype_click"
        }

        object DEVICE {
            const val DEVICELIST_ITEM_CLICK = "home_devicelist_item_click"
            const val SEARCH_SEARCHNEWDEVICE_CLICK = "home_search_searchnewdevice_click"
            const val SEARCH_BIND_CLICK = "home_search_bind_click"
            const val SEARCH_CONNECT_CLICK = "home_search_connect_click"
        }
    }

    object STATISTICS {
        const val ADD_DEVICE_CLICK = "statistics_add_device_click"
        const val CONNECT_CLICK = "statistics_connect_click"
        const val CONSUME_CLICK = "statistics_consume_click"
        const val DISTANCE_CLICK = "statistics_distance_click"
        const val DURATION_CLICK = "statistics_duration_click"
        const val SPORTS_RECORDS_CLICK = "statistics_sports_records_click"
        const val SPORTS_RECORDS_ITEM_CLICK = "statistics_sports_records_item_click"
    }

    object COURSE {
        const val TYPE_CLICK = "course_type_click"
        const val DEVICE_TYPE_CLICK = "course_device_type_click"
        const val COURSE_DETAIL = "course_detail"
        const val DETAIL_FOLLOW_CLICK = "course_detail_follow_click"
        const val DETAIL_START_CLICK = "course_detail_start_click"
        const val VIRTUAL_DETAIL = "virtual_detail"
    }

    object ME {
        const val EDIT_CLICK = "me_edit_click"
        const val SPORTS_DATA_CLICK = "me_sports_data_click"
        const val PRACTICED_COURSES_CLICK = "me_practiced_courses_click"
        const val SAVED_COURSES_CLICK = "me_saved_courses_click"
        const val FOLLOWED_COACH_CLICK = "me_followed_coach_click"
        const val FEEDBACK_CLICK = "me_feedback_click"
        const val SETTING_CLICK = "me_setting_click"
        const val SPORTS_DATA_DEVICETYPE_CLICK = "me_sports_data_devicetype_click"

        object SETTING {
            const val ACCOUNT_EMAIL_CLICK = "me_setting_account_email_click"
            const val ACCOUNT_FB_CLICK = "me_setting_account_fb_click"
            const val ACCOUNT_CHANGEPWD_CLICK = "me_setting_account_changepwd_click"
            const val ACCOUNT_CANCELACCOUNT_CLICK = "me_setting_account_cancelaccount_click"
            const val SERVERS = "me_setting_Servers"
            const val LANGUAGE = "me_setting_Language"
            const val UNIT = "me_setting_unit"
            const val CACHE = "me_setting_cache"
            const val ABOUT = "me_setting_about"
            const val ACCOUNT = "me_setting_Account"
            const val LOGOUT = "me_setting_logout"

        }

    }
}