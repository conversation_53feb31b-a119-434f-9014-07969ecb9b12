package com.merit.common.bean

data class PreReportData(
    val id: String = "", // 1720315276148985858
    val trainType: Int = 0, // 6
    val trainId: String = "", // 1412309316623630337
    val productId: String = "", // 0
    val modelId: String = "", // 0
    val deviceId: String = "", // 0
    val userId: String = "", // 1716730049517113345
    val loginDevice: Int = 0, // 1
    val avgResistance: Int = 0, // 0
    val maxResistance: Int = 0, // 0
    val avgGradient: Int = 0, // 0
    val maxGradient: Int = 0, // 0
    val hot: Int = 0, // 0
    val avgPower: Int = 0, // 0
    val maxPower: Int = 0, // 0
    val metricModel: Int = 0, // 0
    val distance: Int = 0, // 0
    val avgSpeed: Int = 0, // 0
    val maxSpeed: Int = 0, // 0
    val strokeCount: Int = 0, // 0
    val avgStrokeRate: Int = 0, // 0
    val maxStrokeRate: Int = 0, // 0
    val kcal: Int = 0, // 0
    val duration: String = "", // 0
    val status: Int = 0, // 1
    val createTime: String = "", // 2023-11-03 13:41:38
    val createTimestamp: String = "",
    val updateTime: String = "", // 2023-11-03 13:41:38
    val updateTimestamp: String = "",
    val user: UserBean? = null
)