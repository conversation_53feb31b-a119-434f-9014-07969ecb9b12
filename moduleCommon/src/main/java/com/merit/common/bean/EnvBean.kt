package com.merit.common.bean


/**
 * <AUTHOR>
 * desc:
 * @time   2025/6/26
 */
data class EnvBean(
    val environments: Environments,
)

data class Environments(
    val cn: List<EnvironmentItem>?,  // 国内环境列表（字段名 "cn"）
    val sea: List<EnvironmentItem>?  // 海外环境列表（字段名 "sea"）
)

data class EnvironmentItem(
    val id: Int, val name: String, val default: Boolean, val urls: Urls
)

data class Urls(
    val backend: String,          // 所有环境都有（非空）
    val aiBackend: String?,       // 仅 "cn" 环境有（可能为 null）
    val socket: String?,          // 仅 "cn" 环境有（可能为 null）
    val h5: String,               // 所有环境都有（非空）
    val oldH5: String?,           // 仅 "cn" 环境有（可能为 null）
    val aiH5: String?,            // 仅 "cn" 环境有（可能为 null）
    val aiConfig: String?,        // 仅 "cn" 环境有（可能为 null）
    val isTra: String,            // 所有环境都有（非空，值为 "0" 或 "1"）
    val mrkEnv: String?           // 仅 "cn" 环境有（可能为 null）
)