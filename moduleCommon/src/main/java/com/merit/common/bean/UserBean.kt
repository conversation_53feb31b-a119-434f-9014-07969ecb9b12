package com.merit.common.bean

/**
 * author  : ww
 * desc    :
 * time    : 2021/12/20
 */
data class UserBean(
    val age: Int = 0, // 30
    var avatar: String = "", // https://static.merach.com/avatar/20211203/WPvpLzryU5El06Rx.jpeg
    val birthday: String = "", // 2000-01-01
    val city: String = "",
    val expireTime: String = "", // 2023-10-21
    val isMember: Int = 0, // 1
    val isOpenRenew: Int = 0, // 0
    val maxRate: String = "", // 199
    val minRate: String = "", // 99
    var nickName: String = "", // 马东西
    val province: String = "",
    var sex: Int = 0, // 1.男 2.女
    var perUnit: Int = 1, // 1
    val sexDesc: String = "", // 男
    val uid: String = "", // 1451068939481550850
    var hot: Float = 0f,
    val userHealth: UserHealth = UserHealth(),
) {
    data class UserHealth(
        val bmi: Double = 0.0, // 20.4
        val height: String = "0’0”", // 189.0
        val maxRat: Int = 0, // 0
        val reserveRat: Int = 0, // 0
        val restRat: Int = 0, // 0
        val weight: String = "0.0", // 60.0
        val metricHeight: String = "0.0",//公制数据
        val metricWeight: String = "0.0",//公制数据
    )
}
