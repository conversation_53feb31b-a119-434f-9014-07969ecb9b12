package com.merit.common.bean

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * author  : ww
 * desc    : 课程详情
 * time    : 2021-12-24 16:09:59
 */
@Parcelize
data class CourseDetailsBean(
    val avatars: List<String> = listOf(),
    val channel: Int = 0, // 1
    var coachId: String = "", // 1389787922612670466
    val coachPO: CoachPO = CoachPO(),
    val courseActivityPO: CourseActivityPO = CourseActivityPO(),
    val courseCataloguePOS: List<CourseCataloguePOS> = listOf(),
    var courseStatus: Int = 0, // 50
    var courseTime: Int = 0, // 243
    val cover: String = "", // https://static.merach.com/coach/20211124/Z8pXbXAwvT2UxS1L_750x915.jpg
    val crowd: String = "", // 123456
    val distance: Double = 0.0, // 2.5
    var equipmentId: String = "", // 1 一级类型
    val equipmentName: String = "", // 动感单车
    val equipmentType: Int = 1, // 课程类型1大件2、小件
    val gradeDesc: String = "", // M1
    val hotWords: List<String> = listOf(),
    val icon: String = "", // https://static.merach.com/other/20210428/tn81ow6pkevGUIBX.png
    val courseDetailIcon: String = "", // https://static.merach.com/other/20210428/tn81ow6pkevGUIBX.png
    var id: String = "", // 1465499089037082625
    val interactInfoMessages: List<String> = listOf(),
    val interactInfos: List<String> = listOf(),
    val introduce: String = "", // 123456
    val isCollect: Int = 0, // 0
    val isFree: Int = 0, // 0
    val isMake: Int = 0, // 0
    val isVip: Int = 0, // 1
    val kcal: Int = 0, // 220
    val liveTime: String = "", // 2021-12-08 10:40
    var name: String = "", // 11.30 灯光测试课程
    val num: Int = 0, // 88
    val part: String = "", // 123456
    val speed: Double = 0.0, // 50.0
    val speedDesc: String = "", // 最高踏频（rpm）
    val taboo: String = "", // 老年人（年龄大于65岁）、孕妇、残疾人、患有心脑血管疾病、糖尿病、呼吸系统疾病以及其他疾病的人群、运动损伤未痊愈的人群、任何术后尚未康复的人群、其他医嘱建议不适合的人群
    val tagIcon: String = "", // https://static.merach.com/other/20210819/aTBjoez7AVRb8lGt_36x48.png
    val tags: List<String> = listOf(),
    val type: Int = 0, // 2
    var videos: List<CourseVideoDTO> = listOf(),
    val viewPO: ViewPO = ViewPO(),
) : Parcelable {
    @Parcelize
    data class CoachPO(
        val avatar: String = "", // https://static.merach.com/coach/20210507/aU2MAt26vDNJq2jj.png
        val coachId: String = "", // 1389787922612670466
        val cover: String = "", // https://static.merach.com/coach/20210506/Lq0FQoBd1UxImjbf.png
        val images: List<String> = listOf(),
        val introduce: String = "", // FTN营养师认证国家社会体育指导员DTST线性拉伸认证
        val isFollow: Int = 0, // 0
        val name: String = "", // 小宇
        val title: String = "", // MERIT直播教练
    ) : Parcelable

    @Parcelize
    data class CourseActivityPO(
        val activityId: String = "", // 0
        val activityName: String = "",
        val bonusPoints: Int = 0, // 0
        val burnCalories: Int = 0, // 0
        val caloriesScore: Int = 0, // 0
        val isActivity: Int = 0, // 0
        val isBindEquipment: Int = 0, // 0
        val isBindRate: Int = 0, // 0
        val isHeartRate: Int = 0, // 0
        val isPlan: Int = 0, // 0
        val liveBurnCalories: Int = 0, // 0
        val liveCaloriesScore: Int = 0, // 0
        val models: List<String> = listOf(),
        val planId: String = "", // 0
        val planName: String = "",
        val planUserId: String = "", // 0
    ) : Parcelable

    @Parcelize
    data class CourseCataloguePOS(
        val beginTime: Int = 0, // 0
        val endTime: Int = 0, // 540
        val hotWords: List<String> = listOf(),
        val kcal: Double = 0.0, // 0.0
        val name: String = "", // 课程介绍
        val time: Int = 0, // 9
        var color: Int = 0,
    ) : Parcelable

    @Parcelize
    data class CourseVideoDTO(
        val duration: String = "", // 课程时长
        val definition: String = "", // 课程分辨率
        val url: String = "", // url
    ) : Parcelable

    @Parcelize
    data class ViewPO(
        val isView: Int = 0, // 1
        val list: List<ViewPOList> = listOf(),
    ) : Parcelable {
        @Parcelize
        data class ViewPOList(
            val name: String = "", // 距离(Km)
            val value: String = "0.0", // 12.0
        ) : Parcelable
    }
}