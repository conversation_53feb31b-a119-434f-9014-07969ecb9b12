package com.merit.common.bean

data class ExerciseDataNewBean(
    val month: String = "", // 2023-11
    val items: List<Item> = listOf()
) {
    data class Item(
        val id: String = "", // 1724370957846634497
        val trainType: Int = 0, // 6
        val trainId: String = "", // 1692432431547523073
        val trainInfo: TrainInfo? = TrainInfo(),
        val productId: String = "", // 1
        val modelId: String = "", // 1630452448293478401
        val deviceId: String = "", // 0
        val userId: String = "", // 1476464010068865026
        val loginDevice: Int = 0, // 1
        val avgResistance: Double = 0.0, // 0.82
        val maxResistance: Int = 0, // 1
        val avgGradient: Int = 0, // 0
        val maxGradient: Int = 0, // 0
        val hot: Double = 0.0, // 2.8
        val avgPower: Double = 0.0, // 0.07
        val maxPower: Double = 0.0, // 0.1
        val metricModel: Int = 0, // 1
        val distance: Double = 0.0, // 75
        val distanceBs: Double = 0.0, // 46.6
        val avgSpeed: Double = 0.0, // 12.21
        val avgSpeedBs: Double = 0.0, // 7.59
        val maxSpeed: Int = 0, // 18
        val maxSpeedBs: Double = 0.0, // 11.19
        val strokeCount: Int = 0, // 15
        val avgStrokeRate: Double = 0.0, // 40.71
        val maxStrokeRate: Int = 0, // 60
        val kcal: Double = 0.0, // 1.2
        val duration: String = "", // 16
        val status: Int = 0, // 2
        val createTime: String = "", // 2023-11-14 18:17:28
        val createTimestamp: String = "",
        val updateTime: String = "", // 2023-11-14 18:17:45
        val updateTimestamp: String = "",
        val old:Int = 0
    ) {
        data class TrainInfo(
            val trainType: Int = 0, // 6
            val trainId: String = "", // 1692432431547523073
            val title: String = "", // 喜马拉雅山脉骑行
            val duration: String = "", // 45
            val levelDesc: String = "",
            val imageUrl: String = "" // https://static.merach.com/other/20231114/6c58b6f13adb468997c47c3bd43df1ae_339x298.png
        )
    }
}