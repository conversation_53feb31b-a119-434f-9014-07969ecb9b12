package com.merit.common.bean

enum class UnitEnum {
    //U标识无符号 S标识有符号
    U_8, //UNIT8表示规定一个int占8位，8/8=1个字节，所以你需要把int转换成1个字节进行传输，也就是byte[1]。
    U_16, //UNIT16表示规定一个int占16位，16/8=2个字节，所以你需要把int转换成2个字节进行传输，也就是byte[2]。
    U_32,//Unit32表示规定一个int占32位，32/8=4个字节，所以你需要把int转换成4个字节进行传输，也就是byte[4]。
    U_64,//Unit64表示规定一个int占64位，64/8=8个字节，所以你需要把int转换成8个字节进行传输，也就是byte[8]。
    S_8, //UNIT8表示规定一个int占8位，8/8=1个字节，所以你需要把int转换成1个字节进行传输，也就是byte[1]。
    S_16, //UNIT16表示规定一个int占16位，16/8=2个字节，所以你需要把int转换成2个字节进行传输，也就是byte[2]。
    S_32,//Unit32表示规定一个int占32位，32/8=4个字节，所以你需要把int转换成4个字节进行传输，也就是byte[4]。
    S_64,//Unit64表示规定一个int占64位，64/8=8个字节，所以你需要把int转换成8个字节进行传输，也就是byte[8]。
}