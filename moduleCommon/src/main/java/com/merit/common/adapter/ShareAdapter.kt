package com.merit.common.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.merit.common.R
import com.merit.common.bean.ShareBean
import com.merit.common.databinding.DialogShareItemBinding

/**
 * author  : ww
 * desc    :
 * time    : 2022/1/16 11:16 上午
 */
class ShareAdapter :
    BaseQuickAdapter<ShareBean, BaseDataBindingHolder<DialogShareItemBinding>>(
        R.layout.dialog_share_item) {

    override fun convert(
        holder: BaseDataBindingHolder<DialogShareItemBinding>,
        item: ShareBean,
    ) {
        holder.dataBinding?.run {
            bean = item
            executePendingBindings()
        }
    }
}