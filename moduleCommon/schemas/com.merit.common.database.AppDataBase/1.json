{"formatVersion": 1, "database": {"version": 1, "identityHash": "437427d4d0c9b9c74db2b36ea8b36d3a", "entities": [{"tableName": "DeviceNotifyBean", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`time` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` INTEGER NOT NULL, `courseId` TEXT NOT NULL, `equipmentId` TEXT NOT NULL, `modelId` TEXT NOT NULL, `modelName` TEXT NOT NULL, `takeTime` INTEGER NOT NULL, `kcal` INTEGER NOT NULL, `rateKcal` REAL NOT NULL, `distance` REAL NOT NULL, `speed` REAL NOT NULL, `drag` INTEGER NOT NULL, `rat` INTEGER NOT NULL, `num` INTEGER NOT NULL, `maxSpeed` REAL NOT NULL, `power` REAL NOT NULL, `slope` INTEGER NOT NULL, `playTime` INTEGER NOT NULL, `nowDrag` INTEGER NOT NULL, `frequency` INTEGER NOT NULL, `nowSpeed` REAL NOT NULL, `nowSlope` INTEGER NOT NULL, `electricQuantity` INTEGER NOT NULL, `skippingModel` INTEGER NOT NULL, `planId` TEXT NOT NULL, `upDateRat` INTEGER NOT NULL, `lastStatus` INTEGER NOT NULL, `trainHiitVO` TEXT, `unitVal` TEXT NOT NULL)", "fields": [{"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "courseId", "columnName": "courseId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "equipmentId", "columnName": "equipmentId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "modelId", "columnName": "modelId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "modelName", "columnName": "modelName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "takeTime", "columnName": "takeTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "kcal", "columnName": "kcal", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "rateKcal", "columnName": "rateKcal", "affinity": "REAL", "notNull": true}, {"fieldPath": "distance", "columnName": "distance", "affinity": "REAL", "notNull": true}, {"fieldPath": "speed", "columnName": "speed", "affinity": "REAL", "notNull": true}, {"fieldPath": "drag", "columnName": "drag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "rat", "columnName": "rat", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "num", "columnName": "num", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxSpeed", "columnName": "maxSpeed", "affinity": "REAL", "notNull": true}, {"fieldPath": "power", "columnName": "power", "affinity": "REAL", "notNull": true}, {"fieldPath": "slope", "columnName": "slope", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "playTime", "columnName": "playTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nowDrag", "columnName": "nowDrag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "frequency", "columnName": "frequency", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nowSpeed", "columnName": "nowSpeed", "affinity": "REAL", "notNull": true}, {"fieldPath": "nowSlope", "columnName": "nowSlope", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "electricQuantity", "columnName": "electricQuantity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "skipping<PERSON><PERSON><PERSON>", "columnName": "skipping<PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "planId", "columnName": "planId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "upDateRat", "columnName": "upDateRat", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastStatus", "columnName": "lastStatus", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "trainHiitVO", "columnName": "trainHiitVO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "unitVal", "columnName": "unitVal", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["time"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '437427d4d0c9b9c74db2b36ea8b36d3a')"]}}