apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply from: "${rootDir.getAbsolutePath()}/gradle/publishmaven.gradle"
android {
    compileSdkVersion MyVersions.compileSdkVersion
    defaultConfig {
        multiDexEnabled true
        minSdk MyVersions.minSdkVersion
        versionCode MyVersions.versionCode
    }
    buildFeatures {
        dataBinding = true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildTypes {
        release {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }
}
dependencies {
    implementation fileTree(include: ['*.jar', "*.aar"], dir: 'libs')
}

