apply plugin: 'com.android.library'
apply from: "${rootDir.getAbsolutePath()}/gradle/publishmaven.gradle"
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'

android {
    compileSdkVersion MyVersions.compileSdkVersion
    defaultConfig {
        multiDexEnabled true
        minSdk MyVersions.minSdkVersion
        versionCode MyVersions.versionCode
    }

    buildFeatures {
        dataBinding = true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    buildTypes {
        release {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar', "*.aar"], dir: 'libs')
    implementation project(':moduleCommon')
}

