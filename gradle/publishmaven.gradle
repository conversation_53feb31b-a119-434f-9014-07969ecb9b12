apply plugin: 'maven-publish'
// 配置此模块上传 maven 的 groupId
def GROUP_ID = MyModule.AAR_MODULE_PRE
// 配置此模块上传 maven 的 artifactId
def ARTIFACT_ID = project.name.toLowerCase()

afterEvaluate {
    def mGroupId = GROUP_ID
    def mArtifactId = ARTIFACT_ID
    def mVersion = MyVersions.moduleVersionName+"-SNAPSHOT"

    publishing {
        publications {
            release(MavenPublication) {
                from components.release
                groupId = mGroupId
                artifactId = mArtifactId
                version = mVersion
            }
            debug(MavenPublication) {
                from components.debug
                groupId = mGroupId
                artifactId = mArtifactId
                version = mVersion
            }
        }
        repositories {
            maven {
                url = 'https://packages.aliyun.com/maven/repository/2160068-release-Q0kcTH/'
                credentials {
                    username = '650e5bf5a220ae99aea1309c'
                    password = ']qL0i2kqEMrP'
                }
            }
            maven {
                url = 'https://packages.aliyun.com/maven/repository/2160068-snapshot-25MJti/'
                credentials {
                    username = '650e5bf5a220ae99aea1309c'
                    password = ']qL0i2kqEMrP'
                }
            }
        }
    }
}

task sourceJar(type: Jar) {
    from android.sourceSets.main.java.getSrcDirs()
    archiveClassifier = "sources"
}