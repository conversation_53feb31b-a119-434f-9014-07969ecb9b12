if (getStandAloneRunModuleName() == project.name) {
    apply plugin: 'com.android.application'
} else {
    apply plugin: 'com.android.library'
    apply from: "${rootDir.getAbsolutePath()}/gradle/publishmaven.gradle"
}
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'

android {
    compileSdkVersion MyVersions.compileSdkVersion
    defaultConfig {
        if (getStandAloneRunModuleName() == project.name) {
            applicationId "com.merit" + project.name.toLowerCase()
        }
        multiDexEnabled true
        minSdk MyVersions.minSdkVersion
        versionCode MyVersions.versionCode
    }

    buildFeatures {
        dataBinding = true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    buildTypes {
        release {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
            if (getStandAloneRunModuleName() == project.name) {
                manifest.srcFile 'src/main/debug/AndroidManifest.xml'
                main.java.srcDirs += 'src/main/debug'
            } else {
                manifest.srcFile 'src/main/AndroidManifest.xml'
                java {
                    exclude '**/debug/**'
                }
                kotlin {
                    exclude '**/debug/**/*.kt'
                }
            }
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar', "*.aar"], dir: 'libs')
    implementation project(':moduleCommon')
}

def getStandAloneRunModuleName() {
    File file = rootProject.file('local.properties')
    if (file.exists()) {
        InputStream inputStream = rootProject.file('local.properties').newDataInputStream()
        Properties properties = new Properties()
        properties.load(inputStream)

        if (properties.containsKey("standAloneRunModuleName")) {
            return properties.getProperty("standAloneRunModuleName")
        }
    }
    return ""
}
