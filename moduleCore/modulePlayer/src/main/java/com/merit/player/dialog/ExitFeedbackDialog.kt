package com.merit.player.dialog

import android.content.Context
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.merit.player.R
import com.v.base.dialog.VBDialog
import com.merit.player.databinding.PDialogExitFeedbackBinding
import com.merit.player.databinding.PDialogExitFeedbackItemBinding
import com.v.base.utils.*

/**
 * author  : ww
 * desc    : 训练退出反馈
 * time    : 2022-11-10 16:11:44
 */
class ExitFeedbackDialog(mContext: Context,val list: List<String>) : VBDialog<PDialogExitFeedbackBinding>(mContext) {

    private val mAdapter by lazy {
        mDataBinding.recyclerView.vbLinear(MyAdapter().apply {

            vbConfig(
                onItemClick = { _, view, position ->
                    val item = data[position]
                    listener?.invoke(item)
                    dismiss()
                })
        }). vbDivider {
            setDivider(1)
            setColor("#F6F6F8")
            isCludeVisible = false
        } as MyAdapter
    }

    private var listener: ((content: String) -> Unit)? = null

    fun setClickListener(listener: ((content: String) -> Unit)): ExitFeedbackDialog {
        this.listener = listener
        return this
    }

    override fun initData() {
        mAdapter.vbLoad(list)
    }

    class MyAdapter :
        BaseQuickAdapter<String, BaseDataBindingHolder<PDialogExitFeedbackItemBinding>>(
            R.layout.p_dialog_exit_feedback_item
        ) {

        override fun convert(
            holder: BaseDataBindingHolder<PDialogExitFeedbackItemBinding>,
            item: String,
        ) {
            holder.dataBinding?.run {
                tvContent.text = item
                executePendingBindings()
            }
        }

    }


}