package com.merit.course.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.merit.common.StringKeyConstant
import com.merit.common.utils.StringManagerUtils
import com.merit.course.R
import com.merit.course.bean.SchemeVideoBean
import com.merit.course.databinding.RActivitySchemeVideoMoreItemBinding
import com.v.base.utils.vbGetString

/**
 * author  : ww
 * desc    : 更多课程(主题)
 * time    : 2022-08-16 14:36:48
 */
class SchemeVideoMoreAdapter :
    BaseQuickAdapter<SchemeVideoBean.Record.ThemeCoursePOS, BaseDataBindingHolder<RActivitySchemeVideoMoreItemBinding>>(
        R.layout.r_activity_scheme_video_more_item
    ) {

    override fun convert(
        holder: BaseDataBindingHolder<RActivitySchemeVideoMoreItemBinding>,
        item: SchemeVideoBean.Record.ThemeCoursePOS
    ) {
        holder.dataBinding?.run {
            tvInfo.text =
                item.gradeDesc + " · " + item.courseTime + StringManagerUtils.getString(StringKeyConstant.data_minutes_unit,R.string.string_minute) + " · " + item.kcal.toString() + StringManagerUtils.getString(StringKeyConstant.data_kcal_unit,R.string.string_kcal)

            bean = item
            executePendingBindings()
        }
    }
}