package com.merit.course.adapter

import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.marginStart
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.merit.common.StringKeyConstant
import com.merit.common.utils.StringManagerUtils
import com.merit.course.R
import com.merit.course.bean.RecordVideoBean
import com.merit.course.bean.SchemeVideoBean
import com.merit.course.databinding.RFragmentRecordVideoItemBinding
import com.merit.course.databinding.RFragmentSchemeVideoItemBinding
import com.merit.course.databinding.RFragmentSchemeVideoItemItemBinding
import com.v.base.utils.vbConfig
import com.v.base.utils.vbDivider
import com.v.base.utils.vbDp2px2Int
import com.v.base.utils.vbLinear
import com.v.base.utils.vbLoad
import com.v.base.utils.vbSetViewMargins


/**
 * author  : ww
 * desc    :
 * time    : 2021/12/20
 */


//录播课程
class RecordVideoAdapter(val vbLinearHorizontal: Boolean = false) :
    BaseQuickAdapter<RecordVideoBean.Record, BaseDataBindingHolder<RFragmentRecordVideoItemBinding>>(R.layout.r_fragment_record_video_item) {


    override fun convert(
        holder: BaseDataBindingHolder<RFragmentRecordVideoItemBinding>,
        item: RecordVideoBean.Record,
    ) {
        if (vbLinearHorizontal) {
            holder.dataBinding?.baseContainer?.apply {
                updateLayoutParams<ViewGroup.LayoutParams> {
                    width = 250.vbDp2px2Int()
                    height = 188.vbDp2px2Int()
                }
                val params = getLayoutParams() as RecyclerView.LayoutParams
                params.setMargins(0, 0, 0, 0)
                params.marginStart = 0
                params.marginEnd = 0
                setLayoutParams(params)
            }
        }
        holder.dataBinding?.run {
            bean = item
            val sb = StringBuffer()
            sb.append(
                "${item.courseTime.toString()}${
                    StringManagerUtils.getString(
                        StringKeyConstant.data_minutes_unit, R.string.string_minute
                    )
                }"
            )
            sb.append(" · ")
            sb.append(item.gradeDesc)
            sb.append(" · ")
            sb.append(
                "${item.kcal.toString()}${
                    StringManagerUtils.getString(
                        StringKeyConstant.data_kcal_unit, R.string.string_consume_unit
                    )
                }"
            )
            sb.append(" · ")
            sb.append(item.coachName)

            tvMsg.text = sb.toString()
            proTv.visibility = if (item.isVip==1) View.VISIBLE else View.GONE

            executePendingBindings()
        }
    }
}


//主题课程
class ThemeAdapter(val onItemClick: (view: View, SchemeVideoBean.Record.ThemeCoursePOS) -> Unit) :
    BaseQuickAdapter<SchemeVideoBean.Record, BaseDataBindingHolder<RFragmentSchemeVideoItemBinding>>(
        R.layout.r_fragment_scheme_video_item
    ) {

    init {
        addChildClickViewIds(R.id.tvMore)
    }

    override fun convert(
        holder: BaseDataBindingHolder<RFragmentSchemeVideoItemBinding>,
        item: SchemeVideoBean.Record,
    ) {
        holder.dataBinding?.run {
            ivIcon.vbLoad(item.themeMap, 16)
            if (item.total >= 3) {
                tvMore.visibility = View.VISIBLE
            } else {
                tvMore.visibility = View.GONE
            }
            if (recyclerView.tag == null) {
                val mAdapterRecommend by lazy {
                    recyclerView.vbLinear(ThemeChildAdapter()).apply {
                        vbDivider {
                            setDivider(12)
                            isCludeVisible = false
                        }
                        vbConfig(onItemClick = { adapter, view, position ->
                            onItemClick.invoke(
                                view, this.data[position] as SchemeVideoBean.Record.ThemeCoursePOS
                            )
                        })
                    } as ThemeChildAdapter
                }
                recyclerView.setHasFixedSize(true)
                recyclerView.isNestedScrollingEnabled = false
                recyclerView.setItemViewCacheSize(600)
                val recycledViewPool: RecyclerView.RecycledViewPool = RecyclerView.RecycledViewPool()
                recyclerView.setRecycledViewPool(recycledViewPool)

                mAdapterRecommend.vbLoad(item.themeCoursePOS)

                recyclerView.tag = mAdapterRecommend
            } else {
                val mAdapterRecommend = recyclerView.tag as ThemeChildAdapter
                mAdapterRecommend.vbLoad(item.themeCoursePOS)
            }

            executePendingBindings()
        }
    }
}

//主题课程的子类数据
class ThemeChildAdapter :
    BaseQuickAdapter<SchemeVideoBean.Record.ThemeCoursePOS, BaseDataBindingHolder<RFragmentSchemeVideoItemItemBinding>>(
        R.layout.r_fragment_scheme_video_item_item
    ) {

    override fun convert(
        holder: BaseDataBindingHolder<RFragmentSchemeVideoItemItemBinding>,
        item: SchemeVideoBean.Record.ThemeCoursePOS,
    ) {
        holder.dataBinding?.run {

            tvInfo.text = item.gradeDesc + " · " + item.courseTime + StringManagerUtils.getString(
                StringKeyConstant.data_minutes_unit, R.string.string_minute
            ) + " · " + item.kcal.toString() + StringManagerUtils.getString(
                StringKeyConstant.data_kcal_unit, R.string.string_kcal
            )
            proTv.visibility = if (item.isVip==1) View.VISIBLE else View.GONE
            bean = item
            executePendingBindings()
        }
    }
}
