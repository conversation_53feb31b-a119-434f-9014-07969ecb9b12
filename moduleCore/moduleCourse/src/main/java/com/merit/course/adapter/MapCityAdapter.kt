package com.merit.course.adapter

import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.merit.course.R
import com.merit.course.bean.MapHomeDataBean
import com.merit.course.databinding.RAdapterMapCityBinding
import com.v.base.utils.vbLoad
import java.math.BigDecimal
import java.math.RoundingMode

class MapCityAdapter :
    BaseQuickAdapter<MapHomeDataBean, BaseDataBindingHolder<RAdapterMapCityBinding>>(R.layout.r_adapter_map_city) {

    override fun convert(holder: BaseDataBindingHolder<RAdapterMapCityBinding>, item: MapHomeDataBean) {
        holder.dataBinding?.run {
            ivSmallMap.vbLoad(item.cover)
            tvSmallCity.text = item.cityName
            tvSmallCity.visibility = if (item.cityName == "") View.GONE else View.VISIBLE
            tvKm.text = "${BigDecimal(item.distance.toDouble() / 1000).setScale(2, RoundingMode.DOWN)}km"
        }
    }

}