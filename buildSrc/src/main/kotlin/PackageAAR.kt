import groovy.cli.Option
import org.gradle.api.DefaultTask
import org.gradle.api.Project
import org.gradle.api.artifacts.ProjectDependency
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.Nested
import org.gradle.api.tasks.TaskAction
import org.gradle.kotlin.dsl.withType

abstract class PackageAAR : DefaultTask() {
    @get:Input
    val moduleSortList = mutableListOf<String>()

    @TaskAction
    fun execute() {
        println("projectDependencyGen start ---------------")
        moduleSortList.clear()
        val rootDirNodeList = mutableListOf<DependencyNode>()
        project.childProjects.forEach { childProject ->
            val node = DependencyNode()
            AarUtil.sortChildProjects(childProject.value, node)
            rootDirNodeList.add(node)
        }
        rootDirNodeList.forEach {
            AarUtil.traverseNodes(it, moduleSortList)
        }
    }


}