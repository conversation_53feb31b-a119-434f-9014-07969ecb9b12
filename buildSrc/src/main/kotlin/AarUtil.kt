import org.gradle.api.Project
import org.gradle.api.artifacts.ModuleDependency
import org.gradle.api.artifacts.ProjectDependency
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.withType
import java.util.Properties

object AarUtil {

    fun sortChildProjects(project: Project, node: DependencyNode) {
        val nodeList = mutableListOf<DependencyNode>()
        node.projectName = project.name
        project.childProjects.forEach { childProject ->
            val tempNode = DependencyNode()
            tempNode.projectName = childProject.value.path
            tempNode.next = addDependency(childProject.value)
            nodeList.add(tempNode)
        }
        node.next = nodeList
    }

    fun addDependency(project: Project): List<DependencyNode> {
        val nodeList = mutableListOf<DependencyNode>()
        project.configurations.filter { it.dependencies.size != 0 }.forEach { config ->
            val dependencies = config.dependencies
                .withType<ProjectDependency>()
                .map { it.dependencyProject }
                .filter { it.path != project.path }
                .sorted()
            println("addDependency project = " + project.name + " dependencies size = " + dependencies.size)
            dependencies.forEach {
                val node = DependencyNode()
                node.projectName = it.path
                node.next = addDependency(it)
                nodeList.add(node)
            }
        }
        return nodeList
    }

    fun traverseNodes(node: DependencyNode?, moduleSortList: MutableList<String>) {
        if (node == null) {
            return
        }
        node.next?.forEach { nextNode ->
            traverseNodes(nextNode, moduleSortList)
            if (!moduleSortList.contains(nextNode.projectName)) {
                moduleSortList.add(nextNode.projectName)
            }
        }
    }

    fun useMavenList(target: Project, properties: Properties): List<String> {
        val moduleList = mutableListOf<String>()
        target.subprojects.forEach { project ->
            moduleList.add(project.path)
        }
        val moduleMavenProjectList = mutableListOf<Project>()
        val arrList = (properties.getProperty("aar") ?: "").replace(" ", "").split(",").orEmpty()
        target.subprojects.forEach { project ->
            arrList.filter { it.trim().isNotEmpty() }.forEach { arrName ->
                if (project.path.contains(arrName)) {
                    println("moduleMavenProjectList add = $arrName")
                    moduleMavenProjectList.add(project)
                }
            }
        }
        val moduleMavenList = moduleMavenProjectList.map { it.path }
        moduleMavenList.forEach {
            println("getUseMavenList moduleMavenList = $it")
        }
        return moduleMavenList
    }
}