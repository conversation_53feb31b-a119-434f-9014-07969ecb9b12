object MyVersions {
    const val kotlinVersion = "1.7.20"
    const val compileSdkVersion = 34
    const val minSdkVersion = 26
    const val targetSdkVersion = 34
    const val moduleVersionName = "2.1"
    const val versionCode = 203921
    const val versionName = "3.9.2.1"
}

object MyModule {
    const val AAR_MODULE_PRE = "com.merit.sport.module"
}

object MyLibs {
    val dependencies = arrayOf(
        "com.mrk.common:mrkCommon:1.2.5",//VLibrary
        "io.github.didi:drouter-api:2.4.6",//drouter
        "io.github.didi:drouter-api-page:1.0.0",//drouter
        "io.github.didi:drouter-api-process:1.0.0",//drouter
        "com.tencent:mmkv:1.3.9",//mmkv 用来替代SharedPreferences
        "io.github.jeremyliao:live-event-bus-x:1.8.0",//live-event-bus
        "com.github.hackware1993:MagicIndicator:1.7.0",//tab
        "com.github.JessYanCoding:AndroidAutoSize:v1.2.1",//屏幕自适应
        "com.squareup.okhttp3:okhttp:4.8.0",//glude
        "com.github.xiaohaibin:XBanner:androidx_v1.2.8",//banner 轮播
        "com.github.PhilJay:MPAndroidChart:v3.1.0",//图表
        "com.airbnb.android:lottie:5.2.0",//动画加载库
        "io.reactivex.rxjava2:rxjava:2.2.6",
        "io.reactivex.rxjava2:rxandroid:2.1.1",
        "com.scwang.smart:refresh-layout-kernel:2.0.3",
        "de.hdodenhof:circleimageview:3.1.0",
        "com.blankj:utilcodex:1.31.1",
        "com.andkulikov:transitionseverywhere:2.1.0",
        "com.github.skydoves:balloon:1.5.3",
        "com.google.code.gson:gson:2.8.5",
        "androidx.room:room-runtime:2.5.0",
        "androidx.room:room-ktx:2.5.0",
//        "com.meritsea.flutter:flutter_middleware:0.1.35-SNAPSHOT",
        "com.meritsea.flutter:flutter_middleware:0.1.42",
        "com.binioter:guideview:1.0.0",
        "com.google.android.flexbox:flexbox:3.0.0",
        "androidx.health.connect:connect-client:1.1.0-alpha07",
        "com.github.grantland:android-autofittextview:0.2.1",
    )
    val commonLib = arrayOf(
        ":moduleOther:pickerview",
        ":moduleOther:wheelview",
        ":moduleOther:picselector"
    )
}